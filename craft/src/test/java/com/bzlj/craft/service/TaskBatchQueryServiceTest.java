package com.bzlj.craft.service;

import com.bzlj.craft.dto.ProcessParameterDetailDTO;
import com.bzlj.craft.dto.TaskQueryResultDTO;
import com.bzlj.craft.dto.WorkStepDetailDTO;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 任务批量查询服务测试类
 * 
 * <AUTHOR>
 * @date 2025-06-18
 */
@Slf4j
@SpringBootTest
public class TaskBatchQueryServiceTest {
    
    @Autowired
    private ITaskBatchQueryService taskBatchQueryService;
    
    @Test
    public void testBatchQueryTaskDetails() {
        List<String> taskIds = Arrays.asList("TASK001", "TASK002", "TASK003", "TASK004", "TASK005");
        
        long startTime = System.currentTimeMillis();
        Map<String, TaskQueryResultDTO> results = taskBatchQueryService.batchQueryTaskDetails(taskIds);
        long endTime = System.currentTimeMillis();
        
        log.info("批量查询任务详情耗时: {}ms, 查询任务数: {}, 成功数: {}", 
                endTime - startTime, taskIds.size(), results.size());
        
        results.forEach((taskId, result) -> {
            if (result != null) {
                log.info("任务详情: taskId={}, taskCode={}, workSteps={}, parameters={}", 
                        result.getTaskId(), result.getTaskCode(), 
                        result.getWorkSteps() != null ? result.getWorkSteps().size() : 0,
                        result.getProcessParameters() != null ? result.getProcessParameters().size() : 0);
            }
        });
    }
    
    @Test
    public void testBatchQueryTaskDetailsAsync() {
        List<String> taskIds = Arrays.asList("TASK001", "TASK002", "TASK003", "TASK004", "TASK005");
        
        long startTime = System.currentTimeMillis();
        CompletableFuture<Map<String, TaskQueryResultDTO>> future = 
                taskBatchQueryService.batchQueryTaskDetailsAsync(taskIds);
        Map<String, TaskQueryResultDTO> results = future.join();
        long endTime = System.currentTimeMillis();
        
        log.info("异步批量查询任务详情耗时: {}ms, 查询任务数: {}, 成功数: {}", 
                endTime - startTime, taskIds.size(), results.size());
        
        results.forEach((taskId, result) -> {
            if (result != null) {
                log.info("异步查询结果: taskId={}, taskCode={}, workSteps={}, parameters={}", 
                        result.getTaskId(), result.getTaskCode(), 
                        result.getWorkSteps() != null ? result.getWorkSteps().size() : 0,
                        result.getProcessParameters() != null ? result.getProcessParameters().size() : 0);
            }
        });
    }
    
    @Test
    public void testBatchQueryWorkSteps() {
        List<String> taskIds = Arrays.asList("TASK001", "TASK002", "TASK003");
        
        long startTime = System.currentTimeMillis();
        Map<String, List<WorkStepDetailDTO>> results = taskBatchQueryService.batchQueryWorkSteps(taskIds);
        long endTime = System.currentTimeMillis();
        
        log.info("批量查询工步耗时: {}ms, 查询任务数: {}", endTime - startTime, taskIds.size());
        
        results.forEach((taskId, workSteps) -> {
            log.info("任务工步: taskId={}, workStepCount={}", taskId, workSteps.size());
            workSteps.forEach(workStep -> {
                log.debug("  工步: {}, 顺序: {}, 参数数: {}", 
                        workStep.getWorkStepName(), workStep.getWorkStepOrder(),
                        workStep.getStepParameters() != null ? workStep.getStepParameters().size() : 0);
            });
        });
    }
    
    @Test
    public void testBatchQueryProcessParameters() {
        List<String> taskIds = Arrays.asList("TASK001", "TASK002", "TASK003");
        
        long startTime = System.currentTimeMillis();
        Map<String, List<ProcessParameterDetailDTO>> results = 
                taskBatchQueryService.batchQueryProcessParameters(taskIds);
        long endTime = System.currentTimeMillis();
        
        log.info("批量查询工艺参数耗时: {}ms, 查询任务数: {}", endTime - startTime, taskIds.size());
        
        results.forEach((taskId, parameters) -> {
            log.info("任务参数: taskId={}, parameterCount={}", taskId, parameters.size());
            parameters.forEach(parameter -> {
                log.debug("  参数: {}, 类型: {}, 实际值: {}", 
                        parameter.getParameterName(), parameter.getParameterType(), parameter.getActualValue());
            });
        });
    }
    
    @Test
    public void testQueryPerformanceStats() {
        List<String> taskIds = Arrays.asList("TASK001", "TASK002", "TASK003", "TASK004", "TASK005");
        
        Map<String, Object> stats = taskBatchQueryService.getQueryPerformanceStats(taskIds);
        
        log.info("查询性能统计:");
        stats.forEach((key, value) -> {
            log.info("  {}: {}", key, value);
        });
    }
    
    @Test
    public void testLargeScaleBatchQuery() {
        // 测试大规模批量查询
        List<String> taskIds = Arrays.asList(
                "TASK001", "TASK002", "TASK003", "TASK004", "TASK005",
                "TASK006", "TASK007", "TASK008", "TASK009", "TASK010",
                "TASK011", "TASK012", "TASK013", "TASK014", "TASK015",
                "TASK016", "TASK017", "TASK018", "TASK019", "TASK020"
        );
        
        long startTime = System.currentTimeMillis();
        Map<String, TaskQueryResultDTO> results = taskBatchQueryService.batchQueryTaskDetails(taskIds);
        long endTime = System.currentTimeMillis();
        
        log.info("大规模批量查询耗时: {}ms, 查询任务数: {}, 成功数: {}, 平均每个任务耗时: {}ms", 
                endTime - startTime, taskIds.size(), results.size(), 
                (endTime - startTime) / (double) taskIds.size());
        
        // 统计查询结果
        int totalWorkSteps = 0;
        int totalParameters = 0;
        for (TaskQueryResultDTO result : results.values()) {
            if (result != null) {
                totalWorkSteps += result.getWorkSteps() != null ? result.getWorkSteps().size() : 0;
                totalParameters += result.getProcessParameters() != null ? result.getProcessParameters().size() : 0;
            }
        }
        
        log.info("查询统计: 总工步数={}, 总参数数={}", totalWorkSteps, totalParameters);
    }
    
    @Test
    public void testConcurrentBatchQuery() {
        List<String> taskIds1 = Arrays.asList("TASK001", "TASK002", "TASK003");
        List<String> taskIds2 = Arrays.asList("TASK004", "TASK005", "TASK006");
        List<String> taskIds3 = Arrays.asList("TASK007", "TASK008", "TASK009");
        
        long startTime = System.currentTimeMillis();
        
        // 并发执行多个批量查询
        CompletableFuture<Map<String, TaskQueryResultDTO>> future1 = 
                taskBatchQueryService.batchQueryTaskDetailsAsync(taskIds1);
        CompletableFuture<Map<String, TaskQueryResultDTO>> future2 = 
                taskBatchQueryService.batchQueryTaskDetailsAsync(taskIds2);
        CompletableFuture<Map<String, TaskQueryResultDTO>> future3 = 
                taskBatchQueryService.batchQueryTaskDetailsAsync(taskIds3);
        
        // 等待所有查询完成
        CompletableFuture.allOf(future1, future2, future3).join();
        
        long endTime = System.currentTimeMillis();
        
        Map<String, TaskQueryResultDTO> results1 = future1.join();
        Map<String, TaskQueryResultDTO> results2 = future2.join();
        Map<String, TaskQueryResultDTO> results3 = future3.join();
        
        log.info("并发批量查询耗时: {}ms", endTime - startTime);
        log.info("查询结果: batch1={}, batch2={}, batch3={}", 
                results1.size(), results2.size(), results3.size());
    }
}
