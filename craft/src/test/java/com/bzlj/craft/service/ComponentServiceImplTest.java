package com.bzlj.craft.service;

import com.bzlj.craft.common.BaseTestNGTest;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @description:
 * @date 2025-03-26 16:48
 */
//@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
class ComponentServiceImplTest extends BaseTestNGTest {


    @Autowired
    private IComponentService componentService;

    @Test
    void getRepository() {
        componentService.getRepository();
    }

    @Test
    void getEntityManager() {
        componentService.getEntityManager();
    }

    @Test
    void getDTOClass() {
        componentService.getDTOClass();
    }

    @Test
    void getPOClass() {
        componentService.getPOClass();
    }

    @Test
    void findComponentByIds(){
        componentService.getPOClass();
        componentService.getEntityManager();
        componentService.findComponentByIds(Lists.newArrayList("TaskInfo","WorkStepList","ResultParams",
                "ProductionProcess","OperationLog","AbnormalRecord","TeamInfo",
                "QualityInspection","inputAndOutput","LoadMaterial","ProductionInputMaterial","ProductionOutMaterial"));
    }
}