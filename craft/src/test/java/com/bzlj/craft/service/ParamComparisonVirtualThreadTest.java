package com.bzlj.craft.service;

import com.bzlj.craft.dto.TaskDetailDTO;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * 参数对比虚拟线程优化测试类
 * 
 * <AUTHOR>
 * @date 2025-06-18
 */
@Slf4j
@SpringBootTest
public class ParamComparisonVirtualThreadTest {
    
    @Autowired
    private ITaskDataComparisonService taskDataComparisonService;
    
    @Test
    public void testParamComparisonBasic() {
        // 基础功能测试
        List<String> taskIds = Arrays.asList("TASK001", "TASK002", "TASK003");
        
        long startTime = System.currentTimeMillis();
        List<TaskDetailDTO> results = taskDataComparisonService.paramComparison(taskIds);
        long endTime = System.currentTimeMillis();
        
        log.info("基础参数对比测试完成:");
        log.info("  任务数量: {}", taskIds.size());
        log.info("  成功结果数: {}", results.size());
        log.info("  总耗时: {}ms", endTime - startTime);
        log.info("  平均每个任务耗时: {}ms", (endTime - startTime) / taskIds.size());
        
        // 验证结果
        results.forEach(taskDetail -> {
            log.info("任务详情: taskId={}, taskCode={}, 工步数={}", 
                    taskDetail.getTaskId(), taskDetail.getTaskCode(), 
                    taskDetail.getStepDetails() != null ? taskDetail.getStepDetails().size() : 0);
            
            if (taskDetail.getStepDetails() != null) {
                taskDetail.getStepDetails().forEach(stepDetail -> {
                    log.debug("  工步: {}, 参数数: {}", 
                            stepDetail.getStepName(), 
                            stepDetail.getParamDetails() != null ? stepDetail.getParamDetails().size() : 0);
                });
            }
        });
    }
    
    @Test
    public void testParamComparisonPerformance() {
        // 性能测试 - 中等规模
        List<String> taskIds = Arrays.asList(
                "TASK001", "TASK002", "TASK003", "TASK004", "TASK005",
                "TASK006", "TASK007", "TASK008", "TASK009", "TASK010"
        );
        
        // 执行多轮测试
        int testRounds = 3;
        long totalTime = 0;
        int successRounds = 0;
        
        for (int i = 0; i < testRounds; i++) {
            try {
                log.info("执行第 {} 轮性能测试", i + 1);
                long startTime = System.currentTimeMillis();
                
                List<TaskDetailDTO> results = taskDataComparisonService.paramComparison(taskIds);
                
                long endTime = System.currentTimeMillis();
                long roundTime = endTime - startTime;
                totalTime += roundTime;
                successRounds++;
                
                log.info("第 {} 轮测试结果: 耗时 {}ms, 成功任务数 {}/{}", 
                        i + 1, roundTime, results.size(), taskIds.size());
                
                // 等待一段时间再执行下一轮
                Thread.sleep(1000);
                
            } catch (Exception e) {
                log.error("第 {} 轮测试失败", i + 1, e);
            }
        }
        
        if (successRounds > 0) {
            double averageTime = totalTime / (double) successRounds;
            log.info("虚拟线程性能测试结果:");
            log.info("  测试轮数: {}", testRounds);
            log.info("  成功轮数: {}", successRounds);
            log.info("  任务数量: {}", taskIds.size());
            log.info("  平均耗时: {:.2f}ms", averageTime);
            log.info("  平均每个任务耗时: {:.2f}ms", averageTime / taskIds.size());
            log.info("  性能等级: {}", getPerformanceGrade(averageTime, taskIds.size()));
        }
    }
    
    @Test
    public void testParamComparisonLargeScale() {
        // 大规模测试
        List<String> taskIds = IntStream.rangeClosed(1, 20)
                .mapToObj(i -> String.format("TASK%03d", i))
                .collect(Collectors.toList());
        
        long startTime = System.currentTimeMillis();
        
        try {
            List<TaskDetailDTO> results = taskDataComparisonService.paramComparison(taskIds);
            
            long endTime = System.currentTimeMillis();
            long totalTime = endTime - startTime;
            
            // 统计详细信息
            int totalSteps = results.stream()
                    .mapToInt(task -> task.getStepDetails() != null ? task.getStepDetails().size() : 0)
                    .sum();
            
            int totalParams = results.stream()
                    .flatMap(task -> task.getStepDetails() != null ? task.getStepDetails().stream() : null)
                    .mapToInt(step -> step.getParamDetails() != null ? step.getParamDetails().size() : 0)
                    .sum();
            
            log.info("大规模参数对比测试完成:");
            log.info("  任务数量: {}", taskIds.size());
            log.info("  成功任务数: {}", results.size());
            log.info("  总工步数: {}", totalSteps);
            log.info("  总参数数: {}", totalParams);
            log.info("  总耗时: {}ms", totalTime);
            log.info("  平均每个任务耗时: {:.2f}ms", totalTime / (double) taskIds.size());
            log.info("  平均每个工步耗时: {:.2f}ms", totalSteps > 0 ? totalTime / (double) totalSteps : 0);
            log.info("  平均每个参数耗时: {:.2f}ms", totalParams > 0 ? totalTime / (double) totalParams : 0);
            
        } catch (Exception e) {
            log.error("大规模测试失败", e);
            throw e;
        }
    }
    
    @Test
    public void testParamComparisonConcurrent() {
        // 并发测试
        List<String> taskIds = Arrays.asList("TASK001", "TASK002", "TASK003", "TASK004", "TASK005");
        int concurrentRequests = 5;
        
        log.info("开始并发测试，并发数: {}, 每个请求任务数: {}", concurrentRequests, taskIds.size());
        
        List<CompletableFuture<TestResult>> futures = IntStream.range(0, concurrentRequests)
                .mapToObj(i -> CompletableFuture.supplyAsync(() -> {
                    try {
                        long startTime = System.currentTimeMillis();
                        List<TaskDetailDTO> results = taskDataComparisonService.paramComparison(taskIds);
                        long endTime = System.currentTimeMillis();
                        
                        return new TestResult(i + 1, endTime - startTime, results.size(), true, null);
                    } catch (Exception e) {
                        log.error("并发请求 {} 失败", i + 1, e);
                        return new TestResult(i + 1, -1, 0, false, e.getMessage());
                    }
                }))
                .collect(Collectors.toList());
        
        // 等待所有请求完成
        List<TestResult> results = futures.stream()
                .map(CompletableFuture::join)
                .collect(Collectors.toList());
        
        // 统计结果
        List<TestResult> successResults = results.stream()
                .filter(TestResult::isSuccess)
                .collect(Collectors.toList());
        
        if (!successResults.isEmpty()) {
            double averageDuration = successResults.stream()
                    .mapToLong(TestResult::getDuration)
                    .average()
                    .orElse(0.0);
            
            long maxDuration = successResults.stream()
                    .mapToLong(TestResult::getDuration)
                    .max()
                    .orElse(0L);
            
            long minDuration = successResults.stream()
                    .mapToLong(TestResult::getDuration)
                    .min()
                    .orElse(0L);
            
            log.info("并发测试结果:");
            log.info("  并发请求数: {}", concurrentRequests);
            log.info("  成功请求数: {}", successResults.size());
            log.info("  失败请求数: {}", concurrentRequests - successResults.size());
            log.info("  平均耗时: {:.2f}ms", averageDuration);
            log.info("  最大耗时: {}ms", maxDuration);
            log.info("  最小耗时: {}ms", minDuration);
            log.info("  成功率: {:.2f}%", (successResults.size() / (double) concurrentRequests) * 100);
            
            // 详细结果
            results.forEach(result -> {
                if (result.isSuccess()) {
                    log.debug("请求 {} 成功: 耗时 {}ms, 结果数 {}", 
                            result.getRequestId(), result.getDuration(), result.getResultCount());
                } else {
                    log.debug("请求 {} 失败: {}", result.getRequestId(), result.getErrorMessage());
                }
            });
        }
    }
    
    @Test
    public void testParamComparisonErrorHandling() {
        // 错误处理测试
        List<String> taskIds = Arrays.asList("INVALID_TASK", "TASK002", "ANOTHER_INVALID");
        
        try {
            List<TaskDetailDTO> results = taskDataComparisonService.paramComparison(taskIds);
            
            log.info("错误处理测试结果:");
            log.info("  请求任务数: {}", taskIds.size());
            log.info("  返回结果数: {}", results.size());
            
            results.forEach(taskDetail -> {
                log.info("任务结果: taskId={}, taskCode={}, taskName={}", 
                        taskDetail.getTaskId(), taskDetail.getTaskCode(), taskDetail.getTaskName());
            });
            
        } catch (Exception e) {
            log.info("错误处理测试 - 正确抛出异常: {}", e.getMessage());
        }
    }
    
    @Test
    public void testParamComparisonValidation() {
        // 参数验证测试
        
        // 测试空列表
        try {
            taskDataComparisonService.paramComparison(Arrays.asList());
            log.error("空列表测试失败 - 应该抛出异常");
        } catch (IllegalArgumentException e) {
            log.info("空列表验证成功: {}", e.getMessage());
        }
        
        // 测试null
        try {
            taskDataComparisonService.paramComparison(null);
            log.error("null测试失败 - 应该抛出异常");
        } catch (IllegalArgumentException e) {
            log.info("null验证成功: {}", e.getMessage());
        }
        
        // 测试超大列表
        List<String> largeTasks = IntStream.rangeClosed(1, 150)
                .mapToObj(i -> "TASK" + i)
                .collect(Collectors.toList());
        
        try {
            taskDataComparisonService.paramComparison(largeTasks);
            log.error("超大列表测试失败 - 应该抛出异常");
        } catch (IllegalArgumentException e) {
            log.info("超大列表验证成功: {}", e.getMessage());
        }
    }
    
    /**
     * 根据性能数据评估性能等级
     */
    private String getPerformanceGrade(double averageTime, int taskCount) {
        double timePerTask = averageTime / taskCount;
        
        if (timePerTask < 50) {
            return "优秀 (< 50ms/任务)";
        } else if (timePerTask < 100) {
            return "良好 (50-100ms/任务)";
        } else if (timePerTask < 200) {
            return "一般 (100-200ms/任务)";
        } else if (timePerTask < 500) {
            return "较差 (200-500ms/任务)";
        } else {
            return "很差 (> 500ms/任务)";
        }
    }
    
    /**
     * 测试结果内部类
     */
    private static class TestResult {
        private final int requestId;
        private final long duration;
        private final int resultCount;
        private final boolean success;
        private final String errorMessage;
        
        public TestResult(int requestId, long duration, int resultCount, boolean success, String errorMessage) {
            this.requestId = requestId;
            this.duration = duration;
            this.resultCount = resultCount;
            this.success = success;
            this.errorMessage = errorMessage;
        }
        
        public int getRequestId() { return requestId; }
        public long getDuration() { return duration; }
        public int getResultCount() { return resultCount; }
        public boolean isSuccess() { return success; }
        public String getErrorMessage() { return errorMessage; }
    }
}
