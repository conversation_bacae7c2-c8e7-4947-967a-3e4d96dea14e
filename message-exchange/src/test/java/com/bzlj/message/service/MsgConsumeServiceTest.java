//package com.bzlj.message.service;
//
//import com.bzlj.message.MessageApplication;
//import com.bzlj.message.internaldistribution.service.impl.MsgConsumeServiceImpl;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
//
//@RunWith(SpringJUnit4ClassRunner.class)
//@SpringBootTest(classes = MessageApplication.class)
//public class MsgConsumeServiceTest {
//
//    @Autowired
//    private MsgConsumeServiceImpl msgConsumeServiceImpl;
//
//    /**
//     * 消息中心消息分发
//     */
//    @Test
//    public void consumeMessage() {
//        String str = getMessageStr();
//        String result = msgConsumeServiceImpl.consumeMessage(str);
//        System.err.println(result);
//    }
//
//    /**
//     * XBus消息分发
//     */
//    @Test
//    public void consumeXBusMessage() {
//        String str = getXBusStr();
//        String result = msgConsumeServiceImpl.consumeXBusMessage(str, "MTDZD1", null);
//        System.err.println(result);
//    }
//
//    private String getMessageStr() {
//        return "{\n" +
//                "\"serviceType\": \"\",\n" +
//                "\"__resAppEname__\": \"eplat-auth-service\",\n" +
//                "\"signature\": \"202505041608:admin:IPLAT4CWEB:IPLAT4C:302c021433903c5d9d9c52b55621226ec27cf9d1d888013002147b9c43962e319c1ac4a59d3ca5fc4cba7fb93e40\",\n" +
//                "\"$$remote$$\": \"false\",\n" +
//                "\"isServiceAuth\": \"0\",\n" +
//                "\"methodName\": \"addGroupRelation\",\n" +
//                "\"soaInvokeProtocol\": \"local\",\n" +
//                "\"needCleanDiagnostic\": false,\n" +
//                "\"serviceName\": \"BEESSubj\",\n" +
//                "\"EPES4JUserInfo\": {\n" +
//                "\"loginName\": \"admin\",\n" +
//                "\"userId\": \"admin\",\n" +
//                "\"username\": \"系统管理员\"\n" +
//                "},\n" +
//                "\"__blocks__\": {\n" +
//                "\"Table2\": {\n" +
//                "\"meta\": {\n" +
//                "\"columns\": [\n" +
//                "{\n" +
//                "\"pos\": 0,\n" +
//                "\"name\": \"ID\",\n" +
//                "\"descName\": \"CID\"\n" +
//                "},\n" +
//                "{\n" +
//                "\"pos\": 1,\n" +
//                "\"name\": \"NAME\",\n" +
//                "\"descName\": \"CNAME\"\n" +
//                "}\n" +
//                "]\n" +
//                "},\n" +
//                "\"attr\": {},\n" +
//                "\"rows\": [\n" +
//                "[\n" +
//                "\"子群组标识1\",\n" +
//                "\"子群组英文名1\"\n" +
//                "],\n" +
//                "[\n" +
//                "\"子群组标识2\",\n" +
//                "\"子群组英文名2\"\n" +
//                "]\n" +
//                "]\n" +
//                "},\n" +
//                "\"Table1\": {\n" +
//                "\"meta\": {\n" +
//                "\"columns\": [\n" +
//                "{\n" +
//                "\"pos\": 0,\n" +
//                "\"name\": \"ID\",\n" +
//                "\"descName\": \"CID\",\n" +
//                "\"type\":\"N\"\n" +
//                "},\n" +
//                "{\n" +
//                "\"pos\": 1,\n" +
//                "\"name\": \"NAME\",\n" +
//                "\"descName\": \"CNAME\"\n" +
//                "},\n" +
//                "{\n" +
//                "\"pos\": 2,\n" +
//                "\"name\": \"userid\",\n" +
//                "\"descName\": \"Cuserid\"\n" +
//                "}\n" +
//                "]\n" +
//                "},\n" +
//                "\"attr\": {},\n" +
//                "\"rows\": [\n" +
//                "[\n" +
//                "12.56,\n" +
//                "\"目标父群组英文名\",\n" +
//                "\"操作人用户ID\"\n" +
//                "],\n" +
//                "[\n" +
//                "1212,\n" +
//                "\"目标父群组英文名\",\n" +
//                "\"操作人用户ID\"\n" +
//                "]\n" +
//                "]\n" +
//                "}\n" +
//                "},\n" +
//                "\"__resProjectEname__\": \"eplat\",\n" +
//                "\"__sys__\": {\n" +
//                "\"msg\": \"\",\n" +
//                "\"traceId\": \"0aa5a055121746349439756000010f4\",\n" +
//                "\"detailMsg\": \"\",\n" +
//                "\"name\": \"\",\n" +
//                "\"msgKey\": \"\",\n" +
//                "\"descName\": \"\",\n" +
//                "\"status\": 0\n" +
//                "},\n" +
//                "\"__version__\": \"2.0\",\n" +
//                "\"serviceId\": \"R_BE_ES_14\"\n" +
//                "}";
//    }
//
//    private String getXBusStr() {
//        return "D$C$3$3$I$S$7$5$z$k$l$S$z$E$i$V$p$F$J$m$I$r$l$6$d$E$U$J$W$G$T$2$z$t$I$s$e$G$Z$i$A$W$M$M$8$I$s$P$Q$0$Y$H$H$N$k$6$o$I$0$o$F$Q$o$j$2$z$o$R$7$c$d$Q$H$U$A$E$1$O$s$Q$c$n$g$1$w$x$X$z$0$X$U$R$E$H$V$u$B$P$l$8$9$W$P$f$4$6$ 2";
//    }
//
//}