package com.bzlj.craft.service;

import com.bzlj.craft.dto.TaskQueryResultDTO;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 任务查询服务测试类
 * 
 * <AUTHOR>
 * @date 2025-06-18
 */
@Slf4j
@SpringBootTest
public class TaskQueryServiceTest {
    
    @Autowired
    private ITaskQueryService taskQueryService;
    
    @Test
    public void testQueryTaskDetails() {
        String taskId = "TASK001"; // 替换为实际的任务ID
        
        long startTime = System.currentTimeMillis();
        TaskQueryResultDTO result = taskQueryService.queryTaskDetails(taskId);
        long endTime = System.currentTimeMillis();
        
        log.info("同步查询耗时: {}ms", endTime - startTime);
        
        if (result != null) {
            log.info("查询结果: taskId={}, taskCode={}, workSteps={}, parameters={}", 
                    result.getTaskId(), result.getTaskCode(), 
                    result.getWorkSteps() != null ? result.getWorkSteps().size() : 0,
                    result.getProcessParameters() != null ? result.getProcessParameters().size() : 0);
        } else {
            log.warn("查询结果为空");
        }
    }
    
    @Test
    public void testQueryTaskDetailsAsync() {
        String taskId = "TASK001"; // 替换为实际的任务ID
        
        long startTime = System.currentTimeMillis();
        CompletableFuture<TaskQueryResultDTO> future = taskQueryService.queryTaskDetailsAsync(taskId);
        TaskQueryResultDTO result = future.join();
        long endTime = System.currentTimeMillis();
        
        log.info("异步查询耗时: {}ms", endTime - startTime);
        
        if (result != null) {
            log.info("异步查询结果: taskId={}, taskCode={}, workSteps={}, parameters={}", 
                    result.getTaskId(), result.getTaskCode(), 
                    result.getWorkSteps() != null ? result.getWorkSteps().size() : 0,
                    result.getProcessParameters() != null ? result.getProcessParameters().size() : 0);
        } else {
            log.warn("异步查询结果为空");
        }
    }
    
    @Test
    public void testQueryMultipleTaskDetailsAsync() {
        List<String> taskIds = Arrays.asList("TASK001", "TASK002", "TASK003"); // 替换为实际的任务ID
        
        long startTime = System.currentTimeMillis();
        CompletableFuture<List<TaskQueryResultDTO>> future = taskQueryService.queryMultipleTaskDetailsAsync(taskIds);
        List<TaskQueryResultDTO> results = future.join();
        long endTime = System.currentTimeMillis();
        
        log.info("批量异步查询耗时: {}ms, 查询任务数: {}, 成功数: {}", 
                endTime - startTime, taskIds.size(), results.size());
        
        results.forEach(result -> {
            if (result != null) {
                log.info("批量查询结果: taskId={}, taskCode={}, workSteps={}, parameters={}", 
                        result.getTaskId(), result.getTaskCode(), 
                        result.getWorkSteps() != null ? result.getWorkSteps().size() : 0,
                        result.getProcessParameters() != null ? result.getProcessParameters().size() : 0);
            }
        });
    }
    
    @Test
    public void testPerformanceComparison() {
        List<String> taskIds = Arrays.asList("TASK001", "TASK002", "TASK003", "TASK004", "TASK005");
        
        // 测试串行查询
        long serialStartTime = System.currentTimeMillis();
        for (String taskId : taskIds) {
            taskQueryService.queryTaskDetails(taskId);
        }
        long serialEndTime = System.currentTimeMillis();
        
        // 测试并行查询
        long parallelStartTime = System.currentTimeMillis();
        List<TaskQueryResultDTO> parallelResults = taskQueryService.queryMultipleTaskDetailsAsync(taskIds).join();
        long parallelEndTime = System.currentTimeMillis();
        
        log.info("性能对比 - 串行查询耗时: {}ms, 并行查询耗时: {}ms, 性能提升: {}%", 
                serialEndTime - serialStartTime, 
                parallelEndTime - parallelStartTime,
                ((double)(serialEndTime - serialStartTime) - (parallelEndTime - parallelStartTime)) / 
                (serialEndTime - serialStartTime) * 100);
    }
}
