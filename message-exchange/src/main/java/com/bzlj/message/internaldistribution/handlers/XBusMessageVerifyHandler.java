package com.bzlj.message.internaldistribution.handlers;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.util.StringUtils;
import com.bzlj.message.common.util.JsonUtils;
import com.bzlj.message.internaldistribution.dto.MessageSendDTO;
import com.bzlj.message.internaldistribution.dto.QueueSendDTO;
import com.bzlj.message.internaldistribution.entity.mongo.MessageRules;
import com.bzlj.message.internaldistribution.entity.mongo.TopicMap;
import com.bzlj.message.internaldistribution.enums.MessageExceptionEnum;
import com.bzlj.message.internaldistribution.queue.QueueManagement;
import com.bzlj.message.internaldistribution.repository.mongo.MessageRulesRepository;
import com.bzlj.message.internaldistribution.repository.mongo.TopicMapRepository;
import com.bzlj.message.internaldistribution.util.Constants;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * XBus消息验证处理器
 *
 * <AUTHOR>
 * @date 2025/5/13 16:39
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class XBusMessageVerifyHandler {

    private final TopicMapRepository topicMapRepository;
    private final MessageRulesRepository messageRulesRepository;
    private final MessageVerifyHandler messageVerifyHandler;

    /**
     * XBus消息验证入队
     *
     * @param jsonStr
     * @param serviceId
     */
//    @Async
    public void verifyHandler(String jsonStr, String serviceId, LocalDateTime receiveTime) {
        try {
            log.info("XBus入参电文:{}", jsonStr);

            //是否是有效电文验证
            TopicMap isExist = topicMapRepository.findByTelegraphTextCode(serviceId);
            if (StrUtil.isEmpty(serviceId) || ObjUtil.isNull(isExist)) {
                messageVerifyHandler.insertMessageException(isExist,
                        jsonStr,
                        "无效电文",
                        MessageExceptionEnum.NOT_SEND_EXCEPTION.getType(),
                        receiveTime);
                return;
            }

            //电文验证
            ObjectNode convertJson = JsonUtils.createObjectNode();
            if (!dataVerifyRules(jsonStr, serviceId, convertJson)) {
                messageVerifyHandler.insertMessageException(isExist,
                        jsonStr,
                        "格式验证失败",
                        MessageExceptionEnum.NOT_SEND_EXCEPTION.getType(),
                        receiveTime);
                return;
            }

            //验证通过后加入队列
            QueueSendDTO queueSendDTO = new QueueSendDTO().setTopic(isExist.getTopic())
                    .setId(serviceId)
                    .setPayload(getSendDTO(serviceId, isExist.getType(), convertJson));
            if (!QueueManagement.mainQueue().offer(queueSendDTO)) {
                messageVerifyHandler.insertMessageException(isExist,
                        jsonStr,
                        "队列加入失败",
                        MessageExceptionEnum.ENQUEUE_EXCEPTION.getType(),
                        receiveTime);
            }

        } catch (Exception e) {
            TopicMap isExist = topicMapRepository.findByTelegraphTextCode(serviceId);
            messageVerifyHandler.insertMessageException(isExist,
                    jsonStr,
                    e.getMessage(),
                    MessageExceptionEnum.NOT_SEND_EXCEPTION.getType(),
                    receiveTime);
        }

    }

    /**
     * 数据校验
     * <p>
     * 循环体存在于电文的最后，截取了非循环体的数据后都属于循环体
     *
     * @param jsonStr
     * @param serviceId
     * @return
     */
    private Boolean dataVerifyRules(String jsonStr, String serviceId, ObjectNode convertJson) {
        Boolean flag;
        List<MessageRules> rules = messageRulesRepository.findByTelegraphTextCode(serviceId);
        if (CollUtil.isEmpty(rules) || ObjUtil.isEmpty(jsonStr)) return Boolean.FALSE;
        rules.sort(Comparator.comparingInt(MessageRules::getSorts));

        List<MessageRules> notLoopList = rules.stream()
                .filter(s -> s.getIsLoop() == 0)
                .sorted(Comparator.comparingInt(MessageRules::getSorts)).toList();
        List<MessageRules> loopList = rules.stream()
                .filter(s -> s.getIsLoop() == 1)
                .sorted(Comparator.comparingInt(MessageRules::getSorts)).toList();

        log.info("入参电文总长度：{}、电文规则总长度：{}、非循环体规则总长度：{}、循环体规则总长度：{}", jsonStr.length(),
                rules.stream().mapToInt(MessageRules::getFieldSize).sum(),
                notLoopList.stream().mapToInt(MessageRules::getFieldSize).sum(),
                loopList.stream().mapToInt(MessageRules::getFieldSize).sum());

        if (jsonStr.contains(Constants.TELEGRAPH_TEXT_SEPARATOR))
            flag = symbolVerifyRules(rules, notLoopList, loopList, jsonStr, convertJson);
        else flag = strVerifyRules(rules, notLoopList, loopList, jsonStr, convertJson);

        return flag;
    }

    /**
     * 纯电文字符串格式校验
     *
     * @param rules
     * @param jsonStr
     * @param convertJson
     * @return
     */
    private Boolean strVerifyRules(List<MessageRules> rules,
                                   List<MessageRules> notLoopList,
                                   List<MessageRules> loopList,
                                   String jsonStr,
                                   ObjectNode convertJson) {

        //非循环体长度
        int notLoopSum = notLoopList.stream().mapToInt(MessageRules::getFieldSize).sum();
        //循环体长度
        int loopSum = loopList.stream().mapToInt(MessageRules::getFieldSize).sum();
        if (jsonStr.length() < notLoopSum + loopSum) return Boolean.FALSE;

        //非循环体字符串长度截取
        int notLoopLength = jsonStr.substring(0, notLoopSum).length();
        //循环体字符串长度截取
        int loopLength = 0;
        if (loopSum > 0) loopLength = jsonStr.substring(notLoopSum).length();

        //基础长度校验
        if (notLoopLength > 0 && loopLength > 0) {
            String loopStr = jsonStr.substring(notLoopSum);
            int loop = loopStr.length() % loopSum;
            //循环体取余不尽为长度异常
            if (loop > 0) return Boolean.FALSE;
        } else if (notLoopLength > 0 && jsonStr.length() != notLoopSum) {
            return Boolean.FALSE;
        } else if (loopLength > 0) {
            int loop = jsonStr.length() % loopSum;
            //循环体取余不尽为长度异常
            if (loop > 0) return Boolean.FALSE;
        }

        //循环体的集合
        List<Map<String, String>> loopItems = new ArrayList<>();
        AtomicInteger currentIndex = new AtomicInteger(0);
        // 第一遍处理所有非循环字段和循环体第一次实例
        Map<String, String> loopItem = new LinkedHashMap<>();
        for (MessageRules rule : rules) {
            if (currentIndex.intValue() >= jsonStr.length()) break;
            String value = jsonStr.substring(currentIndex.intValue(), currentIndex.intValue() + rule.getFieldSize()).trim();
            if (rule.getIsLoop() == 1) {
                // 处理循环体第一次实例
                loopItem.put(rule.getField(), value);
            } else {
                convertJson.put(rule.getField(), value);
            }
            currentIndex.addAndGet(rule.getFieldSize());
        }
        if (!loopItem.isEmpty()) loopItems.add(loopItem);

        //剩余字符串长度
        int remaining = jsonStr.length() - currentIndex.intValue();
        //循环次数
        int maxInstances = 0;
        if (loopSum > 0) maxInstances = remaining / loopSum;

        // 第二遍处理剩余循环体实例
        for (int i = 0; i < maxInstances; i++) {
            Map<String, String> loopItem1 = new LinkedHashMap<>();
            for (MessageRules rule : loopList) {
                String trim = jsonStr.substring(currentIndex.intValue(), currentIndex.intValue() + rule.getFieldSize()).trim();
                loopItem1.put(rule.getField(), trim);
                currentIndex.addAndGet(rule.getFieldSize());
            }
            loopItems.add(loopItem1);
        }

        if (!loopItems.isEmpty()) {
            ArrayNode loopArray = JsonUtils.createArrayNode();
            for (Map<String, String> item : loopItems) {
                ObjectNode itemNode = JsonUtils.createObjectNode();
                for (Map.Entry<String, String> entry : item.entrySet()) {
                    itemNode.put(entry.getKey(), entry.getValue());
                }
                loopArray.add(itemNode);
            }
            convertJson.set(Constants.LOOP_KEY, loopArray);
        }

        return Boolean.TRUE;
    }

    /**
     * 符号分割电文校验
     *
     * @param rules
     * @param jsonStr
     * @param convertJson
     * @return
     */
    private Boolean symbolVerifyRules(List<MessageRules> rules, List<MessageRules> notLoopList, List<MessageRules> loopList, String jsonStr, ObjectNode convertJson) {

        List<String> list = Arrays.asList(jsonStr.split(Constants.BACKSLASH + Constants.TELEGRAPH_TEXT_SEPARATOR, 0));
        if (list.size() < notLoopList.size() + loopList.size()) return Boolean.FALSE;

        List<String> notLoopStrArr = new ArrayList<>();
        List<String> loopStrArr = new ArrayList<>();

        // 第一遍处理所有
        StringBuilder temp = new StringBuilder();
        for (int i = 0; i < Math.min(rules.size(), list.size()); i++) {
            MessageRules messageRules = rules.get(i);
            if (messageRules.getIsLoop() == 1) {
                if (StrUtil.isNotEmpty(temp)) temp.append(",,");
                temp.append(list.get(i));
            } else {
                notLoopStrArr.add(list.get(i));
            }
        }
        if (StrUtil.isNotEmpty(temp)) loopStrArr.add(temp.toString());

        // 剩余的元素根据循环体规则长度进行分组
        int startIndex = rules.size();
        // 循环体取余不尽为长度异常
        if ((list.size() - startIndex) > 0 && (list.size() - startIndex) % loopList.size() > 0) {
            return Boolean.FALSE;
        }
        for (int i = startIndex; i < list.size(); i += loopList.size()) {
            StringBuilder group = new StringBuilder();
            for (int j = 0; j < loopList.size() && i + j < list.size(); j++) {
                if (j > 0) group.append(",,");
                group.append(list.get(i + j));
            }
            loopStrArr.add(group.toString());
        }

        //非循环体验证
        if (!symbolVerify(notLoopList, convertJson, notLoopStrArr)) return Boolean.FALSE;

        //循环体验证
        ArrayNode objects = JsonUtils.createArrayNode();
        for (String str : loopStrArr) {
            ObjectNode jsonObj = JsonUtils.createObjectNode();
            String[] split = str.split(",,", 0);
            if(Arrays.stream(split).allMatch(StringUtils::isBlank)) continue;
            List<String> tempList = Arrays.asList(split);
            if (!symbolVerify(loopList, jsonObj, tempList)) return Boolean.FALSE;
            objects.add(jsonObj);
        }

        if (!objects.isEmpty()) convertJson.set(Constants.LOOP_KEY, objects);

        return Boolean.TRUE;
    }

    /**
     * 数据必填验证
     *
     * @param loopList 验证规则
     * @param jsonObj  jsonObj
     * @param tempList 需验证字段集合
     * @return
     */
    private Boolean symbolVerify(List<MessageRules> loopList, ObjectNode jsonObj, List<String> tempList) {
        for (int i = 0; i < tempList.size(); i++) {
            String node = tempList.get(i);
            MessageRules rule = loopList.get(i);
            jsonObj.put(rule.getField(), node);
            if (rule.getIsRequired() == 1 && StrUtil.isBlank(node)) return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    /**
     * 投递消息组装
     *
     * @param serviceId
     * @param type
     * @param convertJson
     * @return
     */
    private String getSendDTO(String serviceId, String type, ObjectNode convertJson) {
        MessageSendDTO sendDTO = new MessageSendDTO();
        sendDTO.setId(serviceId);
        sendDTO.setPayload(JsonUtils.toJson(convertJson));
        sendDTO.setReceiptTime(DateUtil.toInstant(LocalDateTime.now()).toEpochMilli());
        sendDTO.setType(type);
        return sendDTO.toString();
    }

}

