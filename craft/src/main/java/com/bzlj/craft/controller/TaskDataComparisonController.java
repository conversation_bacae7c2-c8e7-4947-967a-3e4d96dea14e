package com.bzlj.craft.controller;

import com.bzlj.base.response.UnifyResponse;
import com.bzlj.craft.command.DataPointSearchCommand;
import com.bzlj.craft.dto.ContinuousStepDTO;
import com.bzlj.craft.dto.DataPointInfoDTO;
import com.bzlj.craft.dto.TaskDetailDTO;
import com.bzlj.craft.enums.PointMethodType;
import com.bzlj.craft.service.ITaskDataComparisonService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 任务对比分析
 * <AUTHOR>
 * @description:
 * @date 2025-06-10 9:16
 */
@RestController
@RequestMapping("/comparison")
@RequiredArgsConstructor
public class TaskDataComparisonController {

    @Autowired
    private ITaskDataComparisonService taskDataComparisonService;

    /**
     * 参数对比
     * @param taskIds
     * @return
     */
    @PostMapping(value = "/param")
    public UnifyResponse<List<TaskDetailDTO>> paramComparison(@RequestBody List<String> taskIds) {
        List<TaskDetailDTO> taskDetailDTOS = taskDataComparisonService.paramComparison(taskIds);
        return UnifyResponse.success(taskDetailDTOS);
    }

    /**
     *获取连续型工艺参数
     * @param taskId
     * @return
     */
    @GetMapping(value = "/continuous/param")
    public UnifyResponse<List<ContinuousStepDTO>> continuousParams(@RequestParam(value = "taskId") String taskId) {
        List<ContinuousStepDTO> continuousStepDTOS = taskDataComparisonService.continuousParams(taskId);
        return UnifyResponse.success(continuousStepDTOS);
    }
}
