package com.bzlj.craft.service;

import com.bzlj.craft.command.DataPointSearchCommand;
import com.bzlj.craft.dto.DataPointInfoDTO;
import com.bzlj.craft.enums.PointMethodType;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 任务数据对比服务测试类
 *
 * <AUTHOR>
 * @date 2025-06-18
 */
@Slf4j
@SpringBootTest
public class TaskDataComparisonServiceTest {

    @Autowired
    private ITaskDataComparisonService taskDataComparisonService;

    @Test
    public void testGetContinuousDataWithAlignment() {
        // 准备测试数据
        List<String> taskIds = Arrays.asList("TASK001", "TASK002", "TASK003");
        String baseTaskId = "TASK001";

        DataPointSearchCommand command = new DataPointSearchCommand();
        command.setDataCode("TEMP_001");
        command.setCreateEmpty(false);
        command.setFunctionName("mean");
        command.setWindowFrequency("1m");
        command.setTimeUnit("ms");

        PointMethodType pointMethodType = PointMethodType.batch_point;

        long startTime = System.currentTimeMillis();

        try {
            // 执行连续数据查询和对齐
            Map<String, List<DataPointInfoDTO>> alignedData =
                    taskDataComparisonService.getContinuousData(command, taskIds, baseTaskId, pointMethodType);

            long endTime = System.currentTimeMillis();
            log.info("连续数据查询和对齐完成，耗时: {}ms", endTime - startTime);

            // 验证结果
            assertAlignmentResults(alignedData, baseTaskId);

        } catch (Exception e) {
            log.error("测试失败", e);
            throw e;
        }
    }

    @Test
    public void testGetContinuousDataWithDifferentPointMethods() {
        List<String> taskIds = Arrays.asList("TASK001", "TASK002");
        String baseTaskId = "TASK001";

        DataPointSearchCommand command = new DataPointSearchCommand();
        command.setDataCode("PRESSURE_001");
        command.setCreateEmpty(false);

        // 测试不同的点位方法类型
        PointMethodType[] pointMethods = {
                PointMethodType.batch_point,
                PointMethodType.first_point_in_range_date,
                PointMethodType.latest_point_in_range_date,
                PointMethodType.mean_point_in_range_date
        };

        for (PointMethodType pointMethod : pointMethods) {
            log.info("测试点位方法: {}", pointMethod.getMsg());

            try {
                Map<String, List<DataPointInfoDTO>> alignedData =
                        taskDataComparisonService.getContinuousData(command, taskIds, baseTaskId, pointMethod);

                log.info("点位方法 {} 测试成功，返回任务数: {}", pointMethod.getMsg(), alignedData.size());

                // 验证每个任务的数据
                alignedData.forEach((taskId, points) -> {
                    log.info("任务 {} 的对齐后点位数量: {}", taskId, points.size());
                    if (!points.isEmpty()) {
                        DataPointInfoDTO firstPoint = points.get(0);
                        DataPointInfoDTO lastPoint = points.get(points.size() - 1);
                        log.info("  首个点位: ts={}, time={}, value={}",
                                firstPoint.getTs(), firstPoint.getTime(), firstPoint.getV());
                        log.info("  最后点位: ts={}, time={}, value={}",
                                lastPoint.getTs(), lastPoint.getTime(), lastPoint.getV());
                    }
                });

            } catch (Exception e) {
                log.error("点位方法 {} 测试失败", pointMethod.getMsg(), e);
            }
        }
    }

    @Test
    public void testAlignmentAccuracy() {
        List<String> taskIds = Arrays.asList("TASK001", "TASK002", "TASK003");
        String baseTaskId = "TASK001";

        DataPointSearchCommand command = new DataPointSearchCommand();
        command.setDataCode("FLOW_001");
        command.setCreateEmpty(false);
        command.setFunctionName("mean");
        command.setWindowFrequency("30s");

        try {
            Map<String, List<DataPointInfoDTO>> alignedData =
                    taskDataComparisonService.getContinuousData(command, taskIds, baseTaskId, PointMethodType.batch_point);

            // 验证对齐精度
            verifyAlignmentAccuracy(alignedData, baseTaskId);

        } catch (Exception e) {
            log.error("对齐精度测试失败", e);
            throw e;
        }
    }

    @Test
    public void testLargeDatasetAlignment() {
        // 测试大数据集的对齐性能
        List<String> taskIds = Arrays.asList(
                "TASK001", "TASK002", "TASK003", "TASK004", "TASK005",
                "TASK006", "TASK007", "TASK008", "TASK009", "TASK010"
        );
        String baseTaskId = "TASK001";

        DataPointSearchCommand command = new DataPointSearchCommand();
        command.setDataCode("MULTI_POINT");
        command.setCreateEmpty(false);
        command.setFunctionName("mean");
        command.setWindowFrequency("10s");

        long startTime = System.currentTimeMillis();

        try {
            Map<String, List<DataPointInfoDTO>> alignedData =
                    taskDataComparisonService.getContinuousData(command, taskIds, baseTaskId, PointMethodType.batch_point);

            long endTime = System.currentTimeMillis();
            long totalPoints = alignedData.values().stream().mapToInt(List::size).sum();

            log.info("大数据集对齐测试完成:");
            log.info("  任务数量: {}", taskIds.size());
            log.info("  总点位数量: {}", totalPoints);
            log.info("  总耗时: {}ms", endTime - startTime);
            log.info("  平均每个任务耗时: {}ms", (endTime - startTime) / taskIds.size());
            log.info("  平均每个点位耗时: {}ms", totalPoints > 0 ? (endTime - startTime) / (double) totalPoints : 0);

        } catch (Exception e) {
            log.error("大数据集对齐测试失败", e);
            throw e;
        }
    }

    @Test
    public void testVirtualThreadPerformanceComparison() {
        // 测试虚拟线程优化的性能提升
        List<String> taskIds = Arrays.asList(
                "TASK001", "TASK002", "TASK003", "TASK004", "TASK005",
                "TASK006", "TASK007", "TASK008", "TASK009", "TASK010",
                "TASK011", "TASK012", "TASK013", "TASK014", "TASK015"
        );
        String baseTaskId = "TASK001";

        DataPointSearchCommand command = new DataPointSearchCommand();
        command.setDataCode("PERFORMANCE_TEST");
        command.setCreateEmpty(false);
        command.setFunctionName("mean");
        command.setWindowFrequency("5s");

        // 执行多次测试取平均值
        int testRounds = 3;
        long totalTime = 0;
        int successRounds = 0;

        for (int i = 0; i < testRounds; i++) {
            try {
                log.info("执行第 {} 轮性能测试", i + 1);
                long startTime = System.currentTimeMillis();

                Map<String, List<DataPointInfoDTO>> alignedData =
                        taskDataComparisonService.getContinuousData(command, taskIds, baseTaskId, PointMethodType.batch_point);

                long endTime = System.currentTimeMillis();
                long roundTime = endTime - startTime;
                totalTime += roundTime;
                successRounds++;

                long totalPoints = alignedData.values().stream().mapToInt(List::size).sum();
                log.info("第 {} 轮测试结果: 耗时 {}ms, 点位数 {}, 成功任务数 {}",
                        i + 1, roundTime, totalPoints, alignedData.size());

                // 等待一段时间再执行下一轮
                Thread.sleep(1000);

            } catch (Exception e) {
                log.error("第 {} 轮测试失败", i + 1, e);
            }
        }

        if (successRounds > 0) {
            double averageTime = totalTime / (double) successRounds;
            log.info("虚拟线程性能测试结果:");
            log.info("  测试轮数: {}", testRounds);
            log.info("  成功轮数: {}", successRounds);
            log.info("  任务数量: {}", taskIds.size());
            log.info("  平均耗时: {:.2f}ms", averageTime);
            log.info("  平均每个任务耗时: {:.2f}ms", averageTime / taskIds.size());
            log.info("  性能等级: {}", getPerformanceGrade(averageTime, taskIds.size()));
        } else {
            log.error("所有测试轮都失败了");
        }
    }

    @Test
    public void testConcurrentAlignmentStress() {
        // 测试并发对齐的压力测试
        List<String> taskIds = Arrays.asList("TASK001", "TASK002", "TASK003", "TASK004", "TASK005");
        String baseTaskId = "TASK001";

        DataPointSearchCommand command = new DataPointSearchCommand();
        command.setDataCode("STRESS_TEST");
        command.setCreateEmpty(false);
        command.setFunctionName("mean");
        command.setWindowFrequency("1m");

        int concurrentRequests = 5;
        List<CompletableFuture<Long>> futures = new ArrayList<>();

        log.info("开始并发压力测试，并发数: {}", concurrentRequests);

        for (int i = 0; i < concurrentRequests; i++) {
            final int requestId = i + 1;
            CompletableFuture<Long> future = CompletableFuture.supplyAsync(() -> {
                try {
                    log.info("开始执行并发请求 {}", requestId);
                    long startTime = System.currentTimeMillis();

                    Map<String, List<DataPointInfoDTO>> alignedData =
                            taskDataComparisonService.getContinuousData(command, taskIds, baseTaskId, PointMethodType.batch_point);

                    long endTime = System.currentTimeMillis();
                    long duration = endTime - startTime;

                    log.info("并发请求 {} 完成，耗时: {}ms, 结果任务数: {}",
                            requestId, duration, alignedData.size());

                    return duration;
                } catch (Exception e) {
                    log.error("并发请求 {} 失败", requestId, e);
                    return -1L;
                }
            });
            futures.add(future);
        }

        // 等待所有请求完成
        List<Long> results = futures.stream()
                .map(CompletableFuture::join)
                .collect(Collectors.toList());

        // 统计结果
        List<Long> successResults = results.stream()
                .filter(duration -> duration > 0)
                .collect(Collectors.toList());

        if (!successResults.isEmpty()) {
            double averageDuration = successResults.stream().mapToLong(Long::longValue).average().orElse(0.0);
            long maxDuration = successResults.stream().mapToLong(Long::longValue).max().orElse(0L);
            long minDuration = successResults.stream().mapToLong(Long::longValue).min().orElse(0L);

            log.info("并发压力测试结果:");
            log.info("  并发请求数: {}", concurrentRequests);
            log.info("  成功请求数: {}", successResults.size());
            log.info("  失败请求数: {}", concurrentRequests - successResults.size());
            log.info("  平均耗时: {:.2f}ms", averageDuration);
            log.info("  最大耗时: {}ms", maxDuration);
            log.info("  最小耗时: {}ms", minDuration);
            log.info("  成功率: {:.2f}%", (successResults.size() / (double) concurrentRequests) * 100);
        } else {
            log.error("所有并发请求都失败了");
        }
    }

    /**
     * 根据性能数据评估性能等级
     */
    private String getPerformanceGrade(double averageTime, int taskCount) {
        double timePerTask = averageTime / taskCount;

        if (timePerTask < 100) {
            return "优秀 (< 100ms/任务)";
        } else if (timePerTask < 300) {
            return "良好 (100-300ms/任务)";
        } else if (timePerTask < 500) {
            return "一般 (300-500ms/任务)";
        } else if (timePerTask < 1000) {
            return "较差 (500-1000ms/任务)";
        } else {
            return "很差 (> 1000ms/任务)";
        }
    }

    @Test
    public void testErrorHandling() {
        // 测试错误处理
        List<String> taskIds = Arrays.asList("INVALID_TASK", "TASK002");
        String baseTaskId = "INVALID_TASK";

        DataPointSearchCommand command = new DataPointSearchCommand();
        command.setDataCode("TEST_POINT");

        try {
            Map<String, List<DataPointInfoDTO>> alignedData =
                    taskDataComparisonService.getContinuousData(command, taskIds, baseTaskId, PointMethodType.batch_point);

            log.info("错误处理测试 - 应该抛出异常但没有抛出");

        } catch (RuntimeException e) {
            log.info("错误处理测试成功 - 正确抛出异常: {}", e.getMessage());
        } catch (Exception e) {
            log.error("错误处理测试失败 - 抛出了意外的异常类型", e);
            throw e;
        }
    }

    /**
     * 验证对齐结果
     */
    private void assertAlignmentResults(Map<String, List<DataPointInfoDTO>> alignedData, String baseTaskId) {
        log.info("开始验证对齐结果");

        // 验证基准任务存在
        if (!alignedData.containsKey(baseTaskId)) {
            throw new AssertionError("基准任务数据不存在: " + baseTaskId);
        }

        List<DataPointInfoDTO> baseTaskPoints = alignedData.get(baseTaskId);
        if (baseTaskPoints.isEmpty()) {
            log.warn("基准任务没有点位数据");
            return;
        }

        Long baseStartTimestamp = baseTaskPoints.get(0).getTs();
        log.info("基准任务起始时间戳: {}, 对应时间: {}", baseStartTimestamp, baseTaskPoints.get(0).getTime());

        // 验证其他任务的对齐情况
        for (Map.Entry<String, List<DataPointInfoDTO>> entry : alignedData.entrySet()) {
            String taskId = entry.getKey();
            List<DataPointInfoDTO> points = entry.getValue();

            if (points.isEmpty()) {
                log.warn("任务 {} 没有点位数据", taskId);
                continue;
            }

            Long taskStartTimestamp = points.get(0).getTs();
            log.info("任务 {} 对齐后起始时间戳: {}, 对应时间: {}", taskId, taskStartTimestamp, points.get(0).getTime());

            // 验证时间戳是否按升序排列
            for (int i = 1; i < points.size(); i++) {
                if (points.get(i).getTs() < points.get(i - 1).getTs()) {
                    throw new AssertionError("任务 " + taskId + " 的时间戳不是按升序排列");
                }
            }

            log.info("任务 {} 验证通过，点位数量: {}", taskId, points.size());
        }

        log.info("对齐结果验证完成");
    }

    /**
     * 验证对齐精度
     */
    private void verifyAlignmentAccuracy(Map<String, List<DataPointInfoDTO>> alignedData, String baseTaskId) {
        log.info("开始验证对齐精度");

        List<DataPointInfoDTO> basePoints = alignedData.get(baseTaskId);
        if (basePoints.isEmpty()) {
            log.warn("基准任务没有数据，无法验证精度");
            return;
        }

        Long baseOrigin = basePoints.get(0).getTs();

        for (Map.Entry<String, List<DataPointInfoDTO>> entry : alignedData.entrySet()) {
            String taskId = entry.getKey();
            List<DataPointInfoDTO> points = entry.getValue();

            if (points.isEmpty()) continue;

            // 检查是否所有任务的起始时间都对齐到了相同的基准点
            Long taskOrigin = points.get(0).getTs();
            long timeDiff = Math.abs(taskOrigin - baseOrigin);

            log.info("任务 {} 与基准任务的时间差: {}ms", taskId, timeDiff);

            // 允许的时间误差（毫秒）
            long allowedError = 1000; // 1秒误差
            if (timeDiff > allowedError) {
                log.warn("任务 {} 的对齐精度超出允许范围: {}ms > {}ms", taskId, timeDiff, allowedError);
            } else {
                log.info("任务 {} 对齐精度良好", taskId);
            }
        }

        log.info("对齐精度验证完成");
    }
}
