package com.bzlj.craft.dto;

import lombok.Data;

import java.util.List;

/**
 * 任务查询结果DTO
 * 
 * <AUTHOR>
 * @date 2025-06-18
 */
@Data
public class TaskQueryResultDTO {
    
    /**
     * 任务ID
     */
    private String taskId;
    
    /**
     * 任务编码
     */
    private String taskCode;
    
    /**
     * 任务名称
     */
    private String taskName;
    
    /**
     * 执行工步列表
     */
    private List<WorkStepDetailDTO> workSteps;
    
    /**
     * 工艺参数列表
     */
    private List<ProcessParameterDetailDTO> processParameters;
}
