# 任务查询优化改进文档

## 问题描述

1. **MessageHandlerPostProcessor无法读取LoadMaterialTransformHandle类上的MessageHandler注解**
2. **需要根据taskid查询任务下的执行工步及工艺参数定义及其实际值，采用虚拟线程的方式提高查询速率**

## 解决方案

### 1. 修复MessageHandler注解问题

**问题原因：**
- `LoadMaterialTransformHandle`类缺少`@Slf4j`注解
- 缺少`@Component`注解，导致Spring无法正确扫描和注册该Bean

**解决方案：**
- 在`LoadMaterialTransformHandle`类上添加了`@Slf4j`和`@Component`注解
- 确保类能被Spring容器正确管理

**修改文件：**
```
craft/src/main/java/com/bzlj/craft/transform/smelt/handle/LoadMaterialTransformHandle.java
```

### 2. 实现基于虚拟线程的任务查询服务

**核心特性：**
- 使用Java 21的虚拟线程技术
- 并行查询任务的工步和工艺参数
- 支持单个任务查询和批量任务查询
- 提供同步和异步两种查询方式

**新增文件：**

#### 配置类
- `craft/src/main/java/com/bzlj/craft/config/VirtualThreadConfig.java`
  - 配置虚拟线程执行器

#### DTO类
- `craft/src/main/java/com/bzlj/craft/dto/TaskQueryResultDTO.java`
  - 任务查询结果DTO
- `craft/src/main/java/com/bzlj/craft/dto/WorkStepDetailDTO.java`
  - 工步详情DTO
- `craft/src/main/java/com/bzlj/craft/dto/ProcessParameterDetailDTO.java`
  - 工艺参数详情DTO

#### 服务类
- `craft/src/main/java/com/bzlj/craft/service/ITaskQueryService.java`
  - 任务查询服务接口
- `craft/src/main/java/com/bzlj/craft/service/impl/TaskQueryServiceImpl.java`
  - 任务查询服务实现类

#### 控制器
- `craft/src/main/java/com/bzlj/craft/controller/TaskQueryController.java`
  - 提供REST API接口

#### 测试类
- `craft/src/test/java/com/bzlj/craft/service/TaskQueryServiceTest.java`
  - 服务测试类

## API接口说明

### 1. 查询单个任务详情（同步）
```http
GET /api/craft/task-query/details/{taskId}
```

### 2. 查询单个任务详情（异步）
```http
GET /api/craft/task-query/details/async/{taskId}
```

### 3. 批量查询任务详情（同步）
```http
POST /api/craft/task-query/details/batch
Content-Type: application/json

["taskId1", "taskId2", "taskId3"]
```

### 4. 批量查询任务详情（异步）
```http
POST /api/craft/task-query/details/batch/async
Content-Type: application/json

["taskId1", "taskId2", "taskId3"]
```

### 5. 查询任务工步信息
```http
GET /api/craft/task-query/work-steps/{taskId}
```

### 6. 查询任务工艺参数信息
```http
GET /api/craft/task-query/process-parameters/{taskId}
```

## 性能优化特点

### 1. 虚拟线程优势
- **轻量级**：虚拟线程的创建和切换成本极低
- **高并发**：可以创建数百万个虚拟线程
- **简化编程模型**：使用传统的同步编程模型，无需复杂的异步回调

### 2. 并行查询策略
- **任务级并行**：多个任务可以并行查询
- **数据级并行**：单个任务的工步和参数可以并行查询
- **工步级并行**：多个工步的参数可以并行查询

### 3. 查询优化
- **批量查询**：减少数据库访问次数
- **关联查询**：使用JPA的关联查询减少N+1问题
- **异步处理**：提供异步接口支持非阻塞调用

## 使用示例

### Java代码示例
```java
@Autowired
private ITaskQueryService taskQueryService;

// 同步查询单个任务
TaskQueryResultDTO result = taskQueryService.queryTaskDetails("TASK001");

// 异步查询单个任务
CompletableFuture<TaskQueryResultDTO> future = taskQueryService.queryTaskDetailsAsync("TASK001");
TaskQueryResultDTO result = future.join();

// 批量异步查询
List<String> taskIds = Arrays.asList("TASK001", "TASK002", "TASK003");
CompletableFuture<List<TaskQueryResultDTO>> batchFuture = 
    taskQueryService.queryMultipleTaskDetailsAsync(taskIds);
List<TaskQueryResultDTO> results = batchFuture.join();
```

### REST API调用示例
```bash
# 查询单个任务详情
curl -X GET "http://localhost:7900/api/craft/task-query/details/TASK001"

# 批量查询任务详情
curl -X POST "http://localhost:7900/api/craft/task-query/details/batch" \
  -H "Content-Type: application/json" \
  -d '["TASK001", "TASK002", "TASK003"]'
```

## 测试验证

运行测试类验证功能：
```bash
mvn test -Dtest=TaskQueryServiceTest
```

测试内容包括：
- 单个任务查询测试
- 异步查询测试
- 批量查询测试
- 性能对比测试

## 注意事项

1. **数据库连接池配置**：确保数据库连接池大小足够支持并发查询
2. **内存使用**：批量查询时注意内存使用情况
3. **错误处理**：虚拟线程中的异常需要适当处理
4. **监控指标**：建议添加查询性能监控指标

## 预期性能提升

根据虚拟线程的特性和并行查询策略，预期可以获得：
- **单任务查询**：30-50%的性能提升（工步和参数并行查询）
- **批量查询**：60-80%的性能提升（任务级并行）
- **高并发场景**：显著降低线程资源消耗，提高系统吞吐量

## 后续优化建议

1. **缓存机制**：对频繁查询的数据添加缓存
2. **数据预加载**：对关联数据进行预加载
3. **查询优化**：进一步优化SQL查询语句
4. **监控告警**：添加查询性能监控和告警机制
