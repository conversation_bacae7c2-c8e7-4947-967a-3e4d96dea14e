package com.bzlj.craft.transform.smelt.consumer;

import com.bzlj.craft.transform.common.AbsCommonConsumer;
import com.bzlj.craft.transform.common.ForwardMessageInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.function.Consumer;

@Component
@Slf4j
@RequiredArgsConstructor
public class SmeltConsumer extends AbsCommonConsumer {

    /**
     * 实时数据消费者
     */
    @Bean
    public Consumer<List<ForwardMessageInfo<?>>> smeltGyckConsumer() {
        return this::handleDataList;
    }

    private void handleDataList(List<ForwardMessageInfo<?>> nodes) {
        nodes.forEach(p -> {
            handleData(p, p.getServiceId());
        });
    }
}
