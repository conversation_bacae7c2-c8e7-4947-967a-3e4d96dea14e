package com.bzlj.craft.service;

import com.bzlj.craft.command.DataPointSearchCommand;
import com.bzlj.craft.dto.DataPointInfoDTO;
import com.bzlj.craft.enums.PointMethodType;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 任务数据对比服务测试类
 * 
 * <AUTHOR>
 * @date 2025-06-18
 */
@Slf4j
@SpringBootTest
public class TaskDataComparisonServiceTest {
    
    @Autowired
    private ITaskDataComparisonService taskDataComparisonService;
    
    @Test
    public void testGetContinuousDataWithAlignment() {
        // 准备测试数据
        List<String> taskIds = Arrays.asList("TASK001", "TASK002", "TASK003");
        String baseTaskId = "TASK001";
        
        DataPointSearchCommand command = new DataPointSearchCommand();
        command.setDataCode("TEMP_001");
        command.setCreateEmpty(false);
        command.setFunctionName("mean");
        command.setWindowFrequency("1m");
        command.setTimeUnit("ms");
        
        PointMethodType pointMethodType = PointMethodType.batch_point;
        
        long startTime = System.currentTimeMillis();
        
        try {
            // 执行连续数据查询和对齐
            Map<String, List<DataPointInfoDTO>> alignedData = 
                    taskDataComparisonService.getContinuousData(command, taskIds, baseTaskId, pointMethodType);
            
            long endTime = System.currentTimeMillis();
            log.info("连续数据查询和对齐完成，耗时: {}ms", endTime - startTime);
            
            // 验证结果
            assertAlignmentResults(alignedData, baseTaskId);
            
        } catch (Exception e) {
            log.error("测试失败", e);
            throw e;
        }
    }
    
    @Test
    public void testGetContinuousDataWithDifferentPointMethods() {
        List<String> taskIds = Arrays.asList("TASK001", "TASK002");
        String baseTaskId = "TASK001";
        
        DataPointSearchCommand command = new DataPointSearchCommand();
        command.setDataCode("PRESSURE_001");
        command.setCreateEmpty(false);
        
        // 测试不同的点位方法类型
        PointMethodType[] pointMethods = {
                PointMethodType.batch_point,
                PointMethodType.first_point_in_range_date,
                PointMethodType.latest_point_in_range_date,
                PointMethodType.mean_point_in_range_date
        };
        
        for (PointMethodType pointMethod : pointMethods) {
            log.info("测试点位方法: {}", pointMethod.getMsg());
            
            try {
                Map<String, List<DataPointInfoDTO>> alignedData = 
                        taskDataComparisonService.getContinuousData(command, taskIds, baseTaskId, pointMethod);
                
                log.info("点位方法 {} 测试成功，返回任务数: {}", pointMethod.getMsg(), alignedData.size());
                
                // 验证每个任务的数据
                alignedData.forEach((taskId, points) -> {
                    log.info("任务 {} 的对齐后点位数量: {}", taskId, points.size());
                    if (!points.isEmpty()) {
                        DataPointInfoDTO firstPoint = points.get(0);
                        DataPointInfoDTO lastPoint = points.get(points.size() - 1);
                        log.info("  首个点位: ts={}, time={}, value={}", 
                                firstPoint.getTs(), firstPoint.getTime(), firstPoint.getV());
                        log.info("  最后点位: ts={}, time={}, value={}", 
                                lastPoint.getTs(), lastPoint.getTime(), lastPoint.getV());
                    }
                });
                
            } catch (Exception e) {
                log.error("点位方法 {} 测试失败", pointMethod.getMsg(), e);
            }
        }
    }
    
    @Test
    public void testAlignmentAccuracy() {
        List<String> taskIds = Arrays.asList("TASK001", "TASK002", "TASK003");
        String baseTaskId = "TASK001";
        
        DataPointSearchCommand command = new DataPointSearchCommand();
        command.setDataCode("FLOW_001");
        command.setCreateEmpty(false);
        command.setFunctionName("mean");
        command.setWindowFrequency("30s");
        
        try {
            Map<String, List<DataPointInfoDTO>> alignedData = 
                    taskDataComparisonService.getContinuousData(command, taskIds, baseTaskId, PointMethodType.batch_point);
            
            // 验证对齐精度
            verifyAlignmentAccuracy(alignedData, baseTaskId);
            
        } catch (Exception e) {
            log.error("对齐精度测试失败", e);
            throw e;
        }
    }
    
    @Test
    public void testLargeDatasetAlignment() {
        // 测试大数据集的对齐性能
        List<String> taskIds = Arrays.asList(
                "TASK001", "TASK002", "TASK003", "TASK004", "TASK005",
                "TASK006", "TASK007", "TASK008", "TASK009", "TASK010"
        );
        String baseTaskId = "TASK001";
        
        DataPointSearchCommand command = new DataPointSearchCommand();
        command.setDataCode("MULTI_POINT");
        command.setCreateEmpty(false);
        command.setFunctionName("mean");
        command.setWindowFrequency("10s");
        
        long startTime = System.currentTimeMillis();
        
        try {
            Map<String, List<DataPointInfoDTO>> alignedData = 
                    taskDataComparisonService.getContinuousData(command, taskIds, baseTaskId, PointMethodType.batch_point);
            
            long endTime = System.currentTimeMillis();
            long totalPoints = alignedData.values().stream().mapToInt(List::size).sum();
            
            log.info("大数据集对齐测试完成:");
            log.info("  任务数量: {}", taskIds.size());
            log.info("  总点位数量: {}", totalPoints);
            log.info("  总耗时: {}ms", endTime - startTime);
            log.info("  平均每个任务耗时: {}ms", (endTime - startTime) / taskIds.size());
            log.info("  平均每个点位耗时: {}ms", totalPoints > 0 ? (endTime - startTime) / (double) totalPoints : 0);
            
        } catch (Exception e) {
            log.error("大数据集对齐测试失败", e);
            throw e;
        }
    }
    
    @Test
    public void testErrorHandling() {
        // 测试错误处理
        List<String> taskIds = Arrays.asList("INVALID_TASK", "TASK002");
        String baseTaskId = "INVALID_TASK";
        
        DataPointSearchCommand command = new DataPointSearchCommand();
        command.setDataCode("TEST_POINT");
        
        try {
            Map<String, List<DataPointInfoDTO>> alignedData = 
                    taskDataComparisonService.getContinuousData(command, taskIds, baseTaskId, PointMethodType.batch_point);
            
            log.info("错误处理测试 - 应该抛出异常但没有抛出");
            
        } catch (RuntimeException e) {
            log.info("错误处理测试成功 - 正确抛出异常: {}", e.getMessage());
        } catch (Exception e) {
            log.error("错误处理测试失败 - 抛出了意外的异常类型", e);
            throw e;
        }
    }
    
    /**
     * 验证对齐结果
     */
    private void assertAlignmentResults(Map<String, List<DataPointInfoDTO>> alignedData, String baseTaskId) {
        log.info("开始验证对齐结果");
        
        // 验证基准任务存在
        if (!alignedData.containsKey(baseTaskId)) {
            throw new AssertionError("基准任务数据不存在: " + baseTaskId);
        }
        
        List<DataPointInfoDTO> baseTaskPoints = alignedData.get(baseTaskId);
        if (baseTaskPoints.isEmpty()) {
            log.warn("基准任务没有点位数据");
            return;
        }
        
        Long baseStartTimestamp = baseTaskPoints.get(0).getTs();
        log.info("基准任务起始时间戳: {}, 对应时间: {}", baseStartTimestamp, baseTaskPoints.get(0).getTime());
        
        // 验证其他任务的对齐情况
        for (Map.Entry<String, List<DataPointInfoDTO>> entry : alignedData.entrySet()) {
            String taskId = entry.getKey();
            List<DataPointInfoDTO> points = entry.getValue();
            
            if (points.isEmpty()) {
                log.warn("任务 {} 没有点位数据", taskId);
                continue;
            }
            
            Long taskStartTimestamp = points.get(0).getTs();
            log.info("任务 {} 对齐后起始时间戳: {}, 对应时间: {}", taskId, taskStartTimestamp, points.get(0).getTime());
            
            // 验证时间戳是否按升序排列
            for (int i = 1; i < points.size(); i++) {
                if (points.get(i).getTs() < points.get(i - 1).getTs()) {
                    throw new AssertionError("任务 " + taskId + " 的时间戳不是按升序排列");
                }
            }
            
            log.info("任务 {} 验证通过，点位数量: {}", taskId, points.size());
        }
        
        log.info("对齐结果验证完成");
    }
    
    /**
     * 验证对齐精度
     */
    private void verifyAlignmentAccuracy(Map<String, List<DataPointInfoDTO>> alignedData, String baseTaskId) {
        log.info("开始验证对齐精度");
        
        List<DataPointInfoDTO> basePoints = alignedData.get(baseTaskId);
        if (basePoints.isEmpty()) {
            log.warn("基准任务没有数据，无法验证精度");
            return;
        }
        
        Long baseOrigin = basePoints.get(0).getTs();
        
        for (Map.Entry<String, List<DataPointInfoDTO>> entry : alignedData.entrySet()) {
            String taskId = entry.getKey();
            List<DataPointInfoDTO> points = entry.getValue();
            
            if (points.isEmpty()) continue;
            
            // 检查是否所有任务的起始时间都对齐到了相同的基准点
            Long taskOrigin = points.get(0).getTs();
            long timeDiff = Math.abs(taskOrigin - baseOrigin);
            
            log.info("任务 {} 与基准任务的时间差: {}ms", taskId, timeDiff);
            
            // 允许的时间误差（毫秒）
            long allowedError = 1000; // 1秒误差
            if (timeDiff > allowedError) {
                log.warn("任务 {} 的对齐精度超出允许范围: {}ms > {}ms", taskId, timeDiff, allowedError);
            } else {
                log.info("任务 {} 对齐精度良好", taskId);
            }
        }
        
        log.info("对齐精度验证完成");
    }
}
