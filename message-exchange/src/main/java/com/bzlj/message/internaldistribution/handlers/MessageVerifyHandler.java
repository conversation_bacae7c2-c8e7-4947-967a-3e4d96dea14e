package com.bzlj.message.internaldistribution.handlers;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.bzlj.message.common.exception.ValidationException;
import com.bzlj.message.common.util.JsonUtils;
import com.bzlj.message.internaldistribution.dto.MessageSendDTO;
import com.bzlj.message.internaldistribution.dto.QueueSendDTO;
import com.bzlj.message.internaldistribution.entity.mongo.MessageException;
import com.bzlj.message.internaldistribution.entity.mongo.TopicMap;
import com.bzlj.message.internaldistribution.enums.MessageExceptionEnum;
import com.bzlj.message.internaldistribution.queue.QueueManagement;
import com.bzlj.message.internaldistribution.repository.mongo.MessageExceptionRepository;
import com.bzlj.message.internaldistribution.repository.mongo.TopicMapRepository;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.Iterator;
import java.util.Map;

/**
 * 消息中心消息验证处理器
 *
 * <AUTHOR>
 * @date 2025/5/17 12:11
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class MessageVerifyHandler {
    private final TopicMapRepository topicMapRepository;
    private final MessageExceptionRepository messageExceptionRepository;


    /**
     * 消息中心消息验证入队
     * 使用 Jackson 基于 JsonUtils 工具类实现
     *
     * @param jsonStr     消息JSON字符串
     * @param receiveTime 接收时间
     */
//    @Async
    public void verifyHandler(String jsonStr, LocalDateTime receiveTime) {
        String telegramId = "";
        TopicMap isExist = null;
        try {
            log.info("消息中心入参电文:{}", jsonStr);

            // 使用 JsonUtils 解析 JSON
            JsonNode rootNode = JsonUtils.toJsonNode(jsonStr);

            // 检查是否有 messageBody 字段
            JsonNode messageBodyNode = rootNode.get("messageBody");
            if (messageBodyNode == null || messageBodyNode.isNull()) {
                insertMessageException(null,
                        jsonStr,
                        "无效消息,没有 messageBody 字段",
                        MessageExceptionEnum.NOT_SEND_EXCEPTION.getType(),
                        receiveTime);
                return;
            }
            JsonNode mqServiceParamNode = rootNode.get("__mqServiceParam__");
            if (mqServiceParamNode == null || mqServiceParamNode.isNull()) {
                insertMessageException(null,
                        jsonStr,
                        "无效消息,没有 __mqServiceParam__ 字段",
                        MessageExceptionEnum.NOT_SEND_EXCEPTION.getType(),
                        receiveTime);
                return;
            }

            // 解析 messageBody 内容
            String messageBodyStr = messageBodyNode.asText();
            JsonNode messageBodyJsonNode = JsonUtils.toJsonNode(messageBodyStr);

            // 检查 serviceId 和 __blocks__ 字段
            JsonNode telegramIdNode = mqServiceParamNode.get("telegramId");
            JsonNode blocksNode = messageBodyJsonNode.get("__blocks__");

            if (telegramIdNode == null || telegramIdNode.isNull() ||
                blocksNode == null || blocksNode.isNull()) {
                insertMessageException(null,
                        jsonStr,
                        "无效消息,没有 'serviceId' 或 '__blocks__' 字段",
                        MessageExceptionEnum.NOT_SEND_EXCEPTION.getType(),
                        receiveTime);
                return;
            }

            telegramId = telegramIdNode.asText();
            isExist = topicMapRepository.findByTelegraphTextCode(telegramId);
            if (ObjUtil.isNull(isExist)) {
                insertMessageException(isExist,
                        jsonStr,
                        "无效消息,不再电文映射关系中",
                        MessageExceptionEnum.NOT_SEND_EXCEPTION.getType(),
                        receiveTime);
                return;
            }

            // 数据格式验证
            try {
                validateJson(messageBodyStr);
            } catch (ValidationException e) {
                insertMessageException(isExist,
                        jsonStr,
                        "JSON 验证失败: " + e.getMessage(),
                        MessageExceptionEnum.NOT_SEND_EXCEPTION.getType(),
                        receiveTime);
                return;
            }

            // 验证通过后加入队列
            String blocksStr = JsonUtils.toJson(blocksNode);
            String blocks = getSendDTO(telegramId, isExist.getType(), blocksStr);
            QueueSendDTO queueSendDTO = new QueueSendDTO()
                    .setTopic(isExist.getTopic())
                    .setId(telegramId)
                    .setPayload(blocks)
                    .setReceiveTime(receiveTime);
            if (!QueueManagement.mainQueue().offer(queueSendDTO)) {
                insertMessageException(isExist,
                        jsonStr,
                        "队列加入失败",
                        MessageExceptionEnum.ENQUEUE_EXCEPTION.getType(),
                        receiveTime);
            }

        } catch (Exception e) {
            if (StrUtil.isNotBlank(telegramId) && ObjUtil.isNull(isExist))
                isExist = topicMapRepository.findByTelegraphTextCode(telegramId);
            insertMessageException(isExist,
                    jsonStr,
                    e.getMessage(),
                    MessageExceptionEnum.NOT_SEND_EXCEPTION.getType(),
                    receiveTime);
        }
    }


    /**
     * 数据校验
     *
     * @param jsonString
     * @throws ValidationException
     */
    public void validateJson(String jsonString) throws ValidationException {
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            JsonNode rootNode = objectMapper.readTree(jsonString);

            // 1. 检查 __blocks__ 是否存在
            if (!rootNode.has("__blocks__")) {
                throw new ValidationException("Missing '__blocks__' field");
            }
            JsonNode blocksNode = rootNode.get("__blocks__");

            // 2. 遍历所有表（key-value 形式）
            Iterator<Map.Entry<String, JsonNode>> tables = blocksNode.fields();
            while (tables.hasNext()) {
                Map.Entry<String, JsonNode> tableEntry = tables.next();
                String tableName = tableEntry.getKey();
                JsonNode tableNode = tableEntry.getValue();

                // 3. 检查 columns 和 rows 是否存在
                if (!tableNode.has("meta") || !tableNode.get("meta").has("columns")) {
                    throw new ValidationException("'" + tableName + ".meta.columns' is missing or invalid");
                }
                if (!tableNode.has("rows") || !tableNode.get("rows").isArray()) {
                    throw new ValidationException("'" + tableName + ".rows' is missing or not an array");
                }

                JsonNode columnsNode = tableNode.get("meta").get("columns");
                JsonNode rowsNode = tableNode.get("rows");

                // 4. 检查 columns 和 rows 的长度是否匹配
                if (!rowsNode.isEmpty() && columnsNode.size() != getRowLength(rowsNode.get(0))) {
                    throw new ValidationException("'" + tableName + "' columns and rows length mismatch");
                }

                // 5. 遍历每一行数据，校验类型
                for (int rowIndex = 0; rowIndex < rowsNode.size(); rowIndex++) {
                    JsonNode row = rowsNode.get(rowIndex);
                    if (row.size() != columnsNode.size()) {
                        throw new ValidationException("'" + tableName + "' row " + rowIndex + " has incorrect number of values");
                    }

                    for (int colIndex = 0; colIndex < columnsNode.size(); colIndex++) {
                        JsonNode column = columnsNode.get(colIndex);
                        JsonNode cellValue = row.get(colIndex);

                        // 检查 column 是否有 type="N"
                        if (column.has("type") && "N".equals(column.get("type").asText())) {
                            // 必须是数字
                            if (cellValue == null || (!cellValue.isInt() && !cellValue.isDouble())) {
                                throw new ValidationException("'" + tableName + "' column '" + getColumnIdentifier(column) + "' expects a number, but got: " + cellValue);
                            }
                        } else {
                            // 默认是字符串
                            if (cellValue == null || !cellValue.isTextual()) {
                                throw new ValidationException("'" + tableName + "' column '" + getColumnIdentifier(column) + "' expects a string, but got: " + cellValue);
                            }
                        }
                    }
                }
            }

        } catch (IOException e) {
            throw new ValidationException("Invalid JSON format: " + e.getMessage());
        }
    }

    /**
     * 获取 row 的长度（处理空数组情况）
     *
     * @param row
     * @return
     */
    private int getRowLength(JsonNode row) {
        if (row == null || !row.isArray()) {
            return 0;
        }
        return row.size();
    }

    /**
     * 获取 column 的标识（name 或 descName）
     *
     * @param column
     * @return
     */
    private String getColumnIdentifier(JsonNode column) {
        if (column.has("name")) {
            return column.get("name").asText();
        } else if (column.has("descName")) {
            return column.get("descName").asText();
        }
        return "column_" + column.get("pos").asInt(); // 如果都没有，用位置标识
    }

    /**
     * 异常信息落库
     *
     * @param topicMap
     * @param jsonStr        电文
     * @param exceptionsInfo 异常原因
     * @param type           type  异常类型
     * @param receiveTime    接收时间
     */
    public void insertMessageException(TopicMap topicMap,
                                       String jsonStr,
                                       String exceptionsInfo,
                                       Integer type,
                                       LocalDateTime receiveTime) {
        log.info("异常原因：{}!!!!!!!!!!!!!!", exceptionsInfo);
        if (ObjUtil.isNull(topicMap)) topicMap = new TopicMap();
        MessageException messageException = new MessageException()
                .setTopic(topicMap.getTopic())
                .setCreateTime(LocalDateTime.now())
                .setExceptionsInfo(exceptionsInfo)
                .setTelegraphTextCode(topicMap.getTelegraphTextCode()).
                setTelegraphTextBody(jsonStr)
                .setType(type).setReceiveTime(receiveTime);
        messageExceptionRepository.insert(messageException);
    }

    /**
     * 异常信息落库
     *
     * @param topic             topic
     * @param telegraphTextCode 电文号
     * @param jsonStr           电文
     * @param exceptionsInfo    异常原因
     * @param type              type  异常类型
     * @param receiveTime       接收时间
     */
    public void insertMessageException(String topic,
                                       String telegraphTextCode,
                                       String jsonStr,
                                       String exceptionsInfo,
                                       Integer type,
                                       LocalDateTime receiveTime) {
        log.info("异常原因：{}!!!!!!!!!!!!!!", exceptionsInfo);
        MessageException messageException = new MessageException()
                .setTopic(topic)
                .setCreateTime(LocalDateTime.now())
                .setExceptionsInfo(exceptionsInfo)
                .setTelegraphTextCode(telegraphTextCode).
                setTelegraphTextBody(jsonStr)
                .setType(type).setReceiveTime(receiveTime);
        messageExceptionRepository.insert(messageException);
    }

    /**
     * 投递消息组装
     *
     * @param serviceId
     * @param type
     * @param convertJson
     * @return
     */
    private String getSendDTO(String serviceId, String type, String convertJson) {
        MessageSendDTO sendDTO = new MessageSendDTO();
        sendDTO.setId(serviceId);
        sendDTO.setPayload(convertJson);
        sendDTO.setReceiptTime(DateUtil.toInstant(LocalDateTime.now()).toEpochMilli());
        sendDTO.setType(type);
        return sendDTO.toString();
    }

    /**
     * 异常信息落库
     *
     * @param queueSend      信息
     * @param exceptionsInfo 异常原因
     * @param type           type  异常类型
     * <AUTHOR>
     * @date 2025/6/11 16:36
     */
    public void insertMessageException(QueueSendDTO queueSend,
                                       String exceptionsInfo,
                                       Integer type) {
        log.info("异常原因：{}!!!!!!!!!!!!!!", exceptionsInfo);
        MessageSendDTO messageSendDTO = JsonUtils.fromJson(queueSend.getPayload(), MessageSendDTO.class);
        MessageException messageException = new MessageException()
                .setTopic(queueSend.getTopic())
                .setCreateTime(LocalDateTime.now())
                .setExceptionsInfo(exceptionsInfo)
                .setTelegraphTextCode(queueSend.getId()).
                setTelegraphTextBody(JsonUtils.toJson(messageSendDTO))
                .setType(type).setReceiveTime(queueSend.getReceiveTime());
        messageExceptionRepository.insert(messageException);
    }
}
