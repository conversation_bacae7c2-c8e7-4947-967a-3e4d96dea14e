package com.bzlj.craft.transform.smelt.handle;

import bici.bzlj.dataprocess.core.annotation.MessageHandler;
import com.bzlj.craft.transform.common.CommonHandler;
import com.bzlj.craft.transform.smelt.service.CraftParamTransformService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@MessageHandler(messageType = "craft_params", desc = "工艺参数转化")
public class CraftParamTransformHandle extends CommonHandler<String> {

    @Autowired
    private CraftParamTransformService craftParamsTransform;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void transform(String json) {
        craftParamsTransform.craftParamsTransform(json);
    }
}
