package com.bzlj.craft.service.impl;

import com.bzlj.base.search.SearchCondition;
import com.bzlj.base.search.SearchItem;
import com.bzlj.base.search.SearchItems;
import com.bzlj.craft.api.service.IProcessParameterService;
import com.bzlj.craft.dto.*;
import com.bzlj.craft.entity.*;
import com.bzlj.craft.repository.ProcessStepRepository;
import com.bzlj.craft.repository.StepParameterRepository;
import com.bzlj.craft.repository.WorkStepRepository;
import com.bzlj.craft.service.ISurveillanceService;
import com.bzlj.craft.service.ITaskBatchQueryService;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

/**
 * 任务批量查询服务实现类
 * 使用虚拟线程并行查询多个任务的执行工步和工艺参数
 * 
 * <AUTHOR>
 * @date 2025-06-18
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TaskBatchQueryServiceImpl implements ITaskBatchQueryService {
    
    private final ISurveillanceService surveillanceService;
    private final WorkStepRepository workStepRepository;
    private final ProcessStepRepository processStepRepository;
    private final StepParameterRepository stepParameterRepository;
    private final IProcessParameterService processParameterService;
    private final Executor virtualThreadExecutor;
    
    public TaskBatchQueryServiceImpl(ISurveillanceService surveillanceService,
                                   WorkStepRepository workStepRepository,
                                   ProcessStepRepository processStepRepository,
                                   StepParameterRepository stepParameterRepository,
                                   IProcessParameterService processParameterService,
                                   @Qualifier("virtualThreadExecutor") Executor virtualThreadExecutor) {
        this.surveillanceService = surveillanceService;
        this.workStepRepository = workStepRepository;
        this.processStepRepository = processStepRepository;
        this.stepParameterRepository = stepParameterRepository;
        this.processParameterService = processParameterService;
        this.virtualThreadExecutor = virtualThreadExecutor;
    }
    
    @Override
    public Map<String, TaskQueryResultDTO> batchQueryTaskDetails(List<String> taskIds) {
        log.info("开始批量查询任务详情，任务数量: {}", taskIds.size());
        long startTime = System.currentTimeMillis();
        
        try {
            // 使用虚拟线程并行查询
            Map<String, TaskQueryResultDTO> results = batchQueryTaskDetailsAsync(taskIds).join();
            
            long endTime = System.currentTimeMillis();
            log.info("批量查询任务详情完成，任务数量: {}, 成功数量: {}, 耗时: {}ms", 
                    taskIds.size(), results.size(), endTime - startTime);
            
            return results;
        } catch (Exception e) {
            log.error("批量查询任务详情失败", e);
            throw new RuntimeException("批量查询任务详情失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    public CompletableFuture<Map<String, TaskQueryResultDTO>> batchQueryTaskDetailsAsync(List<String> taskIds) {
        return CompletableFuture.supplyAsync(() -> {
            log.debug("开始异步批量查询任务详情，任务数量: {}", taskIds.size());
            
            // 为每个任务创建异步查询任务
            List<CompletableFuture<Map.Entry<String, TaskQueryResultDTO>>> futures = taskIds.stream()
                    .map(taskId -> CompletableFuture.supplyAsync(() -> {
                        try {
                            TaskQueryResultDTO result = queryTaskDetailsByTaskId(taskId);
                            return Map.entry(taskId, result);
                        } catch (Exception e) {
                            log.error("查询任务详情失败，taskId: {}", taskId, e);
                            return Map.entry(taskId, null);
                        }
                    }, virtualThreadExecutor))
                    .collect(Collectors.toList());
            
            // 等待所有查询完成并收集结果
            Map<String, TaskQueryResultDTO> results = futures.stream()
                    .map(CompletableFuture::join)
                    .filter(entry -> entry.getValue() != null)
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            Map.Entry::getValue,
                            (existing, replacement) -> existing,
                            LinkedHashMap::new
                    ));
            
            log.debug("异步批量查询完成，成功查询任务数: {}", results.size());
            return results;
        }, virtualThreadExecutor);
    }
    
    @Override
    public Map<String, List<WorkStepDetailDTO>> batchQueryWorkSteps(List<String> taskIds) {
        log.info("开始批量查询任务工步，任务数量: {}", taskIds.size());
        long startTime = System.currentTimeMillis();
        
        try {
            // 使用虚拟线程并行查询工步
            List<CompletableFuture<Map.Entry<String, List<WorkStepDetailDTO>>>> futures = taskIds.stream()
                    .map(taskId -> CompletableFuture.supplyAsync(() -> {
                        try {
                            List<WorkStepDetailDTO> workSteps = queryWorkStepsByTaskId(taskId);
                            return Map.entry(taskId, workSteps);
                        } catch (Exception e) {
                            log.error("查询任务工步失败，taskId: {}", taskId, e);
                            return Map.entry(taskId, new ArrayList<>());
                        }
                    }, virtualThreadExecutor))
                    .collect(Collectors.toList());
            
            // 收集结果
            Map<String, List<WorkStepDetailDTO>> results = futures.stream()
                    .map(CompletableFuture::join)
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            Map.Entry::getValue,
                            (existing, replacement) -> existing,
                            LinkedHashMap::new
                    ));
            
            long endTime = System.currentTimeMillis();
            log.info("批量查询任务工步完成，任务数量: {}, 耗时: {}ms", taskIds.size(), endTime - startTime);
            
            return results;
        } catch (Exception e) {
            log.error("批量查询任务工步失败", e);
            throw new RuntimeException("批量查询任务工步失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    public Map<String, List<ProcessParameterDetailDTO>> batchQueryProcessParameters(List<String> taskIds) {
        log.info("开始批量查询任务工艺参数，任务数量: {}", taskIds.size());
        long startTime = System.currentTimeMillis();
        
        try {
            // 使用虚拟线程并行查询参数
            List<CompletableFuture<Map.Entry<String, List<ProcessParameterDetailDTO>>>> futures = taskIds.stream()
                    .map(taskId -> CompletableFuture.supplyAsync(() -> {
                        try {
                            List<ProcessParameterDetailDTO> parameters = queryProcessParametersByTaskId(taskId);
                            return Map.entry(taskId, parameters);
                        } catch (Exception e) {
                            log.error("查询任务工艺参数失败，taskId: {}", taskId, e);
                            return Map.entry(taskId, new ArrayList<>());
                        }
                    }, virtualThreadExecutor))
                    .collect(Collectors.toList());
            
            // 收集结果
            Map<String, List<ProcessParameterDetailDTO>> results = futures.stream()
                    .map(CompletableFuture::join)
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            Map.Entry::getValue,
                            (existing, replacement) -> existing,
                            LinkedHashMap::new
                    ));
            
            long endTime = System.currentTimeMillis();
            log.info("批量查询任务工艺参数完成，任务数量: {}, 耗时: {}ms", taskIds.size(), endTime - startTime);
            
            return results;
        } catch (Exception e) {
            log.error("批量查询任务工艺参数失败", e);
            throw new RuntimeException("批量查询任务工艺参数失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    public Map<String, Object> getQueryPerformanceStats(List<String> taskIds) {
        Map<String, Object> stats = new HashMap<>();
        
        // 串行查询性能测试
        long serialStartTime = System.currentTimeMillis();
        for (String taskId : taskIds) {
            queryTaskDetailsByTaskId(taskId);
        }
        long serialEndTime = System.currentTimeMillis();
        long serialTime = serialEndTime - serialStartTime;
        
        // 并行查询性能测试
        long parallelStartTime = System.currentTimeMillis();
        batchQueryTaskDetails(taskIds);
        long parallelEndTime = System.currentTimeMillis();
        long parallelTime = parallelEndTime - parallelStartTime;
        
        // 计算性能提升
        double improvement = serialTime > 0 ? ((double)(serialTime - parallelTime) / serialTime) * 100 : 0;
        
        stats.put("taskCount", taskIds.size());
        stats.put("serialQueryTime", serialTime);
        stats.put("parallelQueryTime", parallelTime);
        stats.put("performanceImprovement", String.format("%.2f%%", improvement));
        stats.put("speedupRatio", serialTime > 0 ? (double)serialTime / parallelTime : 1.0);
        stats.put("testTime", LocalDateTime.now());
        
        log.info("查询性能统计 - 任务数: {}, 串行耗时: {}ms, 并行耗时: {}ms, 性能提升: {}", 
                taskIds.size(), serialTime, parallelTime, String.format("%.2f%%", improvement));
        
        return stats;
    }
    
    /**
     * 根据任务ID查询任务详情
     * 
     * @param taskId 任务ID
     * @return 任务查询结果
     */
    private TaskQueryResultDTO queryTaskDetailsByTaskId(String taskId) {
        try {
            log.debug("开始查询任务详情，taskId: {}", taskId);
            
            // 查询任务基本信息
            ProductionTask task = surveillanceService.findByTaskId(taskId);
            if (task == null) {
                log.warn("任务不存在，taskId: {}", taskId);
                return null;
            }
            
            TaskQueryResultDTO result = new TaskQueryResultDTO();
            result.setTaskId(task.getTaskId());
            result.setTaskCode(task.getTaskCode());
            result.setTaskName(task.getTaskName());
            
            // 使用虚拟线程并行查询工步和参数
            CompletableFuture<List<WorkStepDetailDTO>> workStepsFuture = 
                    CompletableFuture.supplyAsync(() -> queryWorkStepsByTaskId(taskId), virtualThreadExecutor);
            
            CompletableFuture<List<ProcessParameterDetailDTO>> parametersFuture = 
                    CompletableFuture.supplyAsync(() -> queryProcessParametersByTaskId(taskId), virtualThreadExecutor);
            
            // 等待并行查询完成
            CompletableFuture.allOf(workStepsFuture, parametersFuture).join();
            
            result.setWorkSteps(workStepsFuture.join());
            result.setProcessParameters(parametersFuture.join());
            
            log.debug("任务详情查询完成，taskId: {}, 工步数: {}, 参数数: {}", 
                    taskId, result.getWorkSteps().size(), result.getProcessParameters().size());
            
            return result;
        } catch (Exception e) {
            log.error("查询任务详情失败，taskId: {}", taskId, e);
            return null;
        }
    }
    
    /**
     * 根据任务ID查询工步信息
     * 
     * @param taskId 任务ID
     * @return 工步详情列表
     */
    private List<WorkStepDetailDTO> queryWorkStepsByTaskId(String taskId) {
        try {
            ProductionTask task = surveillanceService.findByTaskId(taskId);
            if (task == null || task.getProcess() == null) {
                return new ArrayList<>();
            }
            
            // 查询工序下的工步定义
            List<ProcessStep> processSteps = processStepRepository
                    .findByProcessIdOrderByStepOrderAsc(task.getProcess().getId());
            
            if (CollectionUtils.isEmpty(processSteps)) {
                return new ArrayList<>();
            }
            
            List<String> stepIds = processSteps.stream()
                    .map(ProcessStep::getId)
                    .collect(Collectors.toList());
            
            // 查询任务下的执行工步
            List<WorkStep> workSteps = workStepRepository
                    .findByStepIdInAndTaskTaskIdAndDeleted(stepIds, task.getTaskId(), false);
            
            // 使用虚拟线程并行处理每个工步的参数查询
            List<CompletableFuture<WorkStepDetailDTO>> stepFutures = workSteps.stream()
                    .map(workStep -> CompletableFuture.supplyAsync(() -> 
                            convertToWorkStepDetail(workStep), virtualThreadExecutor))
                    .collect(Collectors.toList());
            
            List<WorkStepDetailDTO> workStepDetails = stepFutures.stream()
                    .map(CompletableFuture::join)
                    .filter(Objects::nonNull)
                    .sorted(Comparator.comparing(WorkStepDetailDTO::getWorkStepOrder))
                    .collect(Collectors.toList());
            
            return workStepDetails;
        } catch (Exception e) {
            log.error("查询任务工步失败，taskId: {}", taskId, e);
            return new ArrayList<>();
        }
    }
    
    /**
     * 根据任务ID查询工艺参数信息
     * 
     * @param taskId 任务ID
     * @return 工艺参数详情列表
     */
    private List<ProcessParameterDetailDTO> queryProcessParametersByTaskId(String taskId) {
        try {
            // 查询任务下的所有工艺参数
            SearchCondition condition = new SearchCondition();
            condition.setSearchItems(SearchItems.builder()
                    .item(new SearchItem("task.taskId", taskId, null, SearchItem.Operator.EQ))
                    .build());
            condition.setOpenProps(Lists.newArrayList("processTextParameter", "processDateParameter", 
                    "processNumParameter", "paramDef"));
            
            List<ProcessParameter> parameters = processParameterService.findEntityWithCondition(condition);
            
            return parameters.stream()
                    .map(this::convertToParameterDetail)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("查询任务工艺参数失败，taskId: {}", taskId, e);
            return new ArrayList<>();
        }
    }
    
    /**
     * 转换工步为详情DTO
     */
    private WorkStepDetailDTO convertToWorkStepDetail(WorkStep workStep) {
        try {
            WorkStepDetailDTO dto = new WorkStepDetailDTO();
            dto.setWorkStepId(workStep.getWorkStepId());
            dto.setWorkStepName(workStep.getWorkStepName());
            dto.setWorkStepOrder(workStep.getWorkStepOrder());
            dto.setStartTime(workStep.getStartTime());
            dto.setEndTime(workStep.getEndTime());
            
            // 查询工步下的参数定义
            if (workStep.getStep() != null) {
                List<StepParameter> stepParameters = stepParameterRepository
                        .findByStepIdOrderByCreatedTime(workStep.getStep().getId());
                
                List<ProcessParameterDetailDTO> stepParamDetails = stepParameters.stream()
                        .map(this::convertStepParameterToDetail)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
                
                dto.setStepParameters(stepParamDetails);
            }
            
            return dto;
        } catch (Exception e) {
            log.error("转换工步详情失败，workStepId: {}", workStep.getWorkStepId(), e);
            return null;
        }
    }
    
    /**
     * 转换工艺参数为详情DTO
     */
    private ProcessParameterDetailDTO convertToParameterDetail(ProcessParameter parameter) {
        try {
            ProcessParameterDetailDTO dto = new ProcessParameterDetailDTO();
            dto.setParameterId(parameter.getParameterId());
            
            if (parameter.getParamDef() != null) {
                dto.setParamDefId(parameter.getParamDef().getParamDefId());
                dto.setParameterName(parameter.getParamDef().getParameterName());
                dto.setParameterType(parameter.getParamDef().getParameterType());
                dto.setUnit(parameter.getParamDef().getUnit());
                dto.setTargetValue(parameter.getParamDef().getTargetValue());
                dto.setUpperLimit(parameter.getParamDef().getUpperLimit());
                dto.setLowerLimit(parameter.getParamDef().getLowerLimit());
            }
            
            // 根据参数类型设置实际值
            if (parameter.getProcessNumParameter() != null) {
                dto.setNumericValue(parameter.getProcessNumParameter().getActualValue());
                dto.setActualValue(parameter.getProcessNumParameter().getActualValue().toString());
                
                // 判断是否合格
                if (dto.getUpperLimit() != null && dto.getLowerLimit() != null) {
                    BigDecimal actualValue = parameter.getProcessNumParameter().getActualValue();
                    dto.setIsQualified(actualValue.compareTo(dto.getLowerLimit()) >= 0 && 
                                     actualValue.compareTo(dto.getUpperLimit()) <= 0);
                }
            } else if (parameter.getProcessTextParameter() != null) {
                dto.setTextValue(parameter.getProcessTextParameter().getActualValue());
                dto.setActualValue(parameter.getProcessTextParameter().getActualValue());
            } else if (parameter.getProcessDateParameter() != null) {
                dto.setDateValue(parameter.getProcessDateParameter().getActualValue());
                dto.setActualValue(parameter.getProcessDateParameter().getActualValue().toString());
            }
            
            dto.setCreatedTime(parameter.getCreatedTime());
            dto.setUpdatedTime(parameter.getUpdatedTime());
            
            return dto;
        } catch (Exception e) {
            log.error("转换工艺参数详情失败，parameterId: {}", parameter.getParameterId(), e);
            return null;
        }
    }
    
    /**
     * 转换工步参数定义为详情DTO
     */
    private ProcessParameterDetailDTO convertStepParameterToDetail(StepParameter stepParameter) {
        try {
            ProcessParameterDetailDTO dto = new ProcessParameterDetailDTO();
            dto.setParamDefId(stepParameter.getParamDefId());
            dto.setParameterName(stepParameter.getParameterName());
            dto.setParameterType(stepParameter.getParameterType());
            dto.setUnit(stepParameter.getUnit());
            dto.setTargetValue(stepParameter.getTargetValue());
            dto.setUpperLimit(stepParameter.getUpperLimit());
            dto.setLowerLimit(stepParameter.getLowerLimit());
            dto.setCreatedTime(stepParameter.getCreatedTime());
            
            return dto;
        } catch (Exception e) {
            log.error("转换工步参数定义失败，paramDefId: {}", stepParameter.getParamDefId(), e);
            return null;
        }
    }
}
