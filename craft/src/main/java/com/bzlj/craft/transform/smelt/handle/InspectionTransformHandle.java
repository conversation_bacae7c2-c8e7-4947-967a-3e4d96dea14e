package com.bzlj.craft.transform.smelt.handle;

import bici.bzlj.dataprocess.core.annotation.MessageHandler;
import com.bzlj.craft.entity.ProductionTask;
import com.bzlj.craft.service.ISurveillanceService;
import com.bzlj.craft.transform.common.CommonHandler;
import com.bzlj.craft.util.JsonUtils;
import com.fasterxml.jackson.databind.JsonNode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

@MessageHandler(messageType = "check_data", desc = "检化验数据转化")
public class InspectionTransformHandle extends CommonHandler<String> {

    @Autowired
    private ISurveillanceService surveillanceService;

    @Transactional(rollbackFor = Exception.class)
    public void transform(String json) {
        JsonNode jsonNode = JsonUtils.toJsonNode(json);
        //查询任务
        String taskCode = jsonNode.get("taskCode").asText();
        ProductionTask task = surveillanceService.findOneByTaskCode(taskCode);

    }
}
