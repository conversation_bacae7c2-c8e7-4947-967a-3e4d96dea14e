package com.bzlj.craft.dto;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 工步详情DTO
 * 
 * <AUTHOR>
 * @date 2025-06-18
 */
@Data
public class WorkStepDetailDTO {
    
    /**
     * 工步ID
     */
    private String workStepId;
    
    /**
     * 工步名称
     */
    private String workStepName;
    
    /**
     * 工步顺序
     */
    private Integer workStepOrder;
    
    /**
     * 工步状态
     */
    private String status;
    
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    
    /**
     * 结束时间
     */
    private LocalDateTime endTime;
    
    /**
     * 工步下的工艺参数
     */
    private List<ProcessParameterDetailDTO> stepParameters;
}
