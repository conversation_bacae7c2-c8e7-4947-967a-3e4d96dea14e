package com.bzlj.craft.entity;

import com.bzlj.base.generator.SnowflakeId;
import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Set;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "production_task")
@EntityListeners(AuditingEntityListener.class)
public class ProductionTask implements Serializable {
    @Id
    @Size(max = 36)
    @Column(name = "task_id", nullable = false, length = 36)
    @SnowflakeId
    @GeneratedValue(generator = "snowflake")
    private String taskId;

    @Size(max = 20)
    @Column(name = "task_code", nullable = false, length = 20)
    private String taskCode;

    @Column(name = "start_time", nullable = false)
    private LocalDateTime startTime;

    @Column(name = "end_time")
    private LocalDateTime endTime;

    @Size(max = 10)
    @Column(name = "workshop_id", nullable = false, length = 10)
    private String workshopId;

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "step_id", nullable = false)
    private ProcessStep step;

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "status_code", nullable = false)
    private SysDictItem statusCode;

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "plant_id", nullable = false)
    private Plant plant;

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "process_id", nullable = false)
    private CraftProcess process;

    @Column(name = "plan_start_time", nullable = false)
    private LocalDateTime planStartTime;

    @Column(name = "plan_end_time")
    private LocalDateTime planEndTime;

    /**
     * 计划生产重量
     */
    @Column(name = "plan_weight", nullable = false, precision = 10, scale = 2)
    private BigDecimal planWeight;

    /**
     * 任务名称
     */
    @Column(name = "task_name")
    private String taskName;

    /**
     * 计划生产数量
     */
    @Column(name = "plan_quantity")
    private Integer planQuantity;

    @OneToMany(mappedBy = "task", cascade = {CascadeType.ALL},orphanRemoval = true,fetch = FetchType.LAZY)
    @JsonIgnore
    @ToString.Exclude
    private Set<TaskEquipment> taskEquipments;

    @OneToMany(mappedBy = "task", cascade = {CascadeType.ALL},orphanRemoval = true,fetch = FetchType.LAZY)
    @JsonIgnore
    @ToString.Exclude
    private Set<TaskMaterial> taskMaterials;

    @OneToMany(mappedBy = "task", cascade = {CascadeType.ALL},orphanRemoval = true,fetch = FetchType.LAZY)
    @JsonIgnore
    @ToString.Exclude
    private Set<ProductionTaskExtend> extendAttr;

    @Column(name = "score", precision = 10, scale = 2)
    private BigDecimal score;

    @Column(name = "deleted")
    private Boolean deleted = false;

    public ProductionTask(String taskId, String taskCode, LocalDateTime startTime, LocalDateTime endTime) {
        this.taskId = taskId;
        this.taskCode = taskCode;
        this.startTime = startTime;
        this.endTime = endTime;
    }

}