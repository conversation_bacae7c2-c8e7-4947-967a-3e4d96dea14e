package com.bzlj.craft.service;

import com.bzlj.base.result.DataResult;
import com.bzlj.base.service.IBaseService;
import com.bzlj.craft.dto.ProductionTaskDTO;
import com.bzlj.craft.dto.ResultParamsDTO;
import com.bzlj.craft.dto.WorkStepDTO;
import com.bzlj.craft.entity.ProductionTask;
import com.bzlj.craft.entity.SysDictItem;
import com.bzlj.craft.enums.PointMethodType;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 在线测控
 * @date 2025-03-10 13:32
 */
public interface ISurveillanceService extends IBaseService<ProductionTask, ProductionTaskDTO,String> {

    /**
     * 查询在线测控列表
     * @param json
     * @return
     */
    DataResult getSurveillanceList(String json);

    /**
     * 查询在线测控列表统计信息
     * @return
     */
    DataResult getTaskStatusStatistics();

    /**
     * 查询在线测控详情的工步列表
     * @param json
     * @return
     */
    DataResult findSurveillanceStep(String json);

    /**
     * 通过任务条件查询工步列表
     * @return
     */
    List<WorkStepDTO> findWorkStepByTask(String taskId,String processId);
    /**
     * 查询在线测控详情的结果型参数
     * @return
     */
    DataResult findStepResultParams(String json);

    /**
     * 查询在线测控详情的操作过程
     * @return
     */
    DataResult findOperation(String json);

    /**
     * 查询检验数据
     * @return
     */
    DataResult findInspectionData(String json);

    /**
     * 查询装料记录
     * @return
     */
    DataResult findLoadMaterialRecorde(String json);

    /**
     * 查询生产物料
     * @return
     */
    DataResult findProductionMaterials(String json);


    /**
     * 查询班组信息
     * @return
     */
    DataResult findTeamInfo(String json);

    /**
     * 查询连续型工艺参数
     * @return
     */
    DataResult findContinuousParams(String json);

    /**
     * 查询异常记录
     * @param json
     * @return
     */
    DataResult findAbnormalRecords(String json);

    /**
     * 获取点位数据信息
     * @param json
     * @param pointMethodType
     * @return
     */
    Object fetchPointData(Object json, PointMethodType pointMethodType);

    /**
     * 查询任务状态
     * @param taskId
     * @return
     */
    String findTaskStatus(String taskId);

    /**
     * 查询扣分明细
     * @param json
     * @return
     */
    DataResult getScoreDetails(String json,String taskId);

    /**
     * 任务撤销
     * @param taskCode
     */
    void deleteTask(String taskCode);

    /**
     * 任务状态变更
     * @param taskCode
     * @param statusCode
     * @return
     */
    void changeStatus(String taskCode, SysDictItem statusCode);

    List<ProductionTask> findByTaskCodes(Collection<String> taskCodes);

    ProductionTask findOneByTaskCode(String taskCode);

    ProductionTask findByTaskIdWithProcess(String taskId);

    /**
     * 根据任务id查询结果型参数
     * @return
     */
    List<ResultParamsDTO> findResultParams(String taskId,String processId);
}
