package com.bzlj.message.internaldistribution.util;

import lombok.extern.slf4j.Slf4j;
import org.redisson.api.*;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.Duration;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * Redis 工具类 - 基于 Redisson 实现
 * 提供静态方法访问，提高使用效率
 *
 * <AUTHOR>
 * @date 2025/6/20
 */
@Slf4j
public class RedisUtils {

    private static RedissonClient staticRedissonClient;

    @Autowired
    public RedisUtils(RedissonClient redissonClient) {
        staticRedissonClient = redissonClient;
    }

    // =============================基础操作=============================

    /**
     * 指定缓存失效时间
     *
     * @param key  键
     * @param time 时间(秒)
     * @return true成功 false失败
     */
    public static boolean expire(String key, long time) {
        try {
            if (time > 0) {
                RBucket<Object> bucket = staticRedissonClient.getBucket(key);
                return bucket.expire(Duration.ofSeconds(time));
            }
            return false;
        } catch (Exception e) {
            log.error("设置过期时间失败, key: {}, time: {}", key, time, e);
            return false;
        }
    }

    /**
     * 根据key获取过期时间
     *
     * @param key 键 不能为null
     * @return 时间(秒) 返回0代表为永久有效
     */
    public static long getExpire(String key) {
        try {
            RBucket<Object> bucket = staticRedissonClient.getBucket(key);
            return bucket.remainTimeToLive() / 1000;
        } catch (Exception e) {
            log.error("获取过期时间失败, key: {}", key, e);
            return -1;
        }
    }

    /**
     * 判断key是否存在
     *
     * @param key 键
     * @return true 存在 false不存在
     */
    public static boolean hasKey(String key) {
        try {
            return staticRedissonClient.getBucket(key).isExists();
        } catch (Exception e) {
            log.error("判断key是否存在失败, key: {}", key, e);
            return false;
        }
    }

    /**
     * 删除缓存
     *
     * @param keys 可以传一个值 或多个
     */
    public static long delete(String... keys) {
        try {
            if (keys != null && keys.length > 0) {
                return staticRedissonClient.getKeys().delete(keys);
            }
            return 0;
        } catch (Exception e) {
            log.error("删除缓存失败, keys: {}", Arrays.toString(keys), e);
            return 0;
        }
    }

    /**
     * 根据pattern删除key
     *
     * @param pattern 匹配模式
     * @return 删除的数量
     */
    public static long deleteByPattern(String pattern) {
        try {
            return staticRedissonClient.getKeys().deleteByPattern(pattern);
        } catch (Exception e) {
            log.error("根据pattern删除key失败, pattern: {}", pattern, e);
            return 0;
        }
    }

    /**
     * 根据pattern查找key
     *
     * @param pattern 匹配模式
     * @return key集合
     */
    public static Set<String> keys(String pattern) {
        try {
            Iterable<String> keys = staticRedissonClient.getKeys().getKeysByPattern(pattern);
            Set<String> keySet = new HashSet<>();
            keys.forEach(keySet::add);
            return keySet;
        } catch (Exception e) {
            log.error("根据pattern查找key失败, pattern: {}", pattern, e);
            return Collections.emptySet();
        }
    }

    // ============================String=============================

    /**
     * 普通缓存获取
     *
     * @param key 键
     * @return 值
     */
    public static Object get(String key) {
        try {
            return key == null ? null : staticRedissonClient.getBucket(key).get();
        } catch (Exception e) {
            log.error("获取缓存失败, key: {}", key, e);
            return null;
        }
    }

    /**
     * 普通缓存放入
     *
     * @param key   键
     * @param value 值
     * @return true成功 false失败
     */
    public static boolean set(String key, Object value) {
        try {
            staticRedissonClient.getBucket(key).set(value);
            return true;
        } catch (Exception e) {
            log.error("设置缓存失败, key: {}, value: {}", key, value, e);
            return false;
        }
    }

    /**
     * 普通缓存放入并设置时间
     *
     * @param key   键
     * @param value 值
     * @param time  时间(秒) time要大于0 如果time小于等于0 将设置无限期
     * @return true成功 false 失败
     */
    public static boolean set(String key, Object value, long time) {
        try {
            if (time > 0) {
                staticRedissonClient.getBucket(key).set(value, time, TimeUnit.SECONDS);
            } else {
                set(key, value);
            }
            return true;
        } catch (Exception e) {
            log.error("设置缓存失败, key: {}, value: {}, time: {}", key, value, time, e);
            return false;
        }
    }

    /**
     * 递增
     *
     * @param key   键
     * @param delta 要增加几(大于0)
     * @return 递增后的值
     */
    public static long incr(String key, long delta) {
        try {
            if (delta < 0) {
                throw new RuntimeException("递增因子必须大于0");
            }
            RAtomicLong atomicLong = staticRedissonClient.getAtomicLong(key);
            return atomicLong.addAndGet(delta);
        } catch (Exception e) {
            log.error("递增失败, key: {}, delta: {}", key, delta, e);
            return 0;
        }
    }

    /**
     * 递增1
     *
     * @param key 键
     * @return 递增后的值
     */
    public static long incr(String key) {
        return incr(key, 1);
    }

    /**
     * 递减
     *
     * @param key   键
     * @param delta 要减少几(小于0)
     * @return 递减后的值
     */
    public static long decr(String key, long delta) {
        try {
            if (delta < 0) {
                throw new RuntimeException("递减因子必须大于0");
            }
            RAtomicLong atomicLong = staticRedissonClient.getAtomicLong(key);
            return atomicLong.addAndGet(-delta);
        } catch (Exception e) {
            log.error("递减失败, key: {}, delta: {}", key, delta, e);
            return 0;
        }
    }

    /**
     * 递减1
     *
     * @param key 键
     * @return 递减后的值
     */
    public static long decr(String key) {
        return decr(key, 1);
    }

    // ================================Hash=================================

    /**
     * HashGet
     *
     * @param key  键 不能为null
     * @param item 项 不能为null
     * @return 值
     */
    public static Object hget(String key, String item) {
        try {
            return staticRedissonClient.getMap(key).get(item);
        } catch (Exception e) {
            log.error("Hash获取失败, key: {}, item: {}", key, item, e);
            return null;
        }
    }

    /**
     * 获取hashKey对应的所有键值
     *
     * @param key 键
     * @return 对应的多个键值
     */
    public Map<Object, Object> hmget(String key) {
        try {
            return staticRedissonClient.getMap(key).readAllMap();
        } catch (Exception e) {
            log.error("Hash获取所有键值失败, key: {}", key, e);
            return Collections.emptyMap();
        }
    }

    /**
     * HashSet
     *
     * @param key 键
     * @param map 对应多个键值
     * @return true 成功 false 失败
     */
    public boolean hmset(String key, Map<String, Object> map) {
        try {
            staticRedissonClient.getMap(key).putAll(map);
            return true;
        } catch (Exception e) {
            log.error("Hash设置多个键值失败, key: {}, map: {}", key, map, e);
            return false;
        }
    }

    /**
     * 向一张hash表中放入数据,如果不存在将创建
     *
     * @param key   键
     * @param item  项
     * @param value 值
     * @return true 成功 false失败
     */
    public boolean hset(String key, String item, Object value) {
        try {
            staticRedissonClient.getMap(key).put(item, value);
            return true;
        } catch (Exception e) {
            log.error("Hash设置单个键值失败, key: {}, item: {}, value: {}", key, item, value, e);
            return false;
        }
    }

    /**
     * 删除hash表中的值
     *
     * @param key  键 不能为null
     * @param item 项 可以使多个 不能为null
     */
    public void hdel(String key, Object... item) {
        try {
            staticRedissonClient.getMap(key).fastRemove(item);
        } catch (Exception e) {
            log.error("Hash删除失败, key: {}, item: {}", key, Arrays.toString(item), e);
        }
    }

    /**
     * 判断hash表中是否有该项的值
     *
     * @param key  键 不能为null
     * @param item 项 不能为null
     * @return true 存在 false不存在
     */
    public boolean hHasKey(String key, String item) {
        try {
            return staticRedissonClient.getMap(key).containsKey(item);
        } catch (Exception e) {
            log.error("Hash判断是否存在失败, key: {}, item: {}", key, item, e);
            return false;
        }
    }

    // ============================分布式锁=============================

    /**
     * 获取分布式锁
     *
     * @param lockKey 锁的key
     * @return 锁对象
     */
    public RLock getLock(String lockKey) {
        return staticRedissonClient.getLock(lockKey);
    }

    /**
     * 尝试获取锁
     *
     * @param lockKey   锁的key
     * @param waitTime  等待时间
     * @param leaseTime 锁持有时间
     * @param unit      时间单位
     * @return 是否获取成功
     */
    public boolean tryLock(String lockKey, long waitTime, long leaseTime, TimeUnit unit) {
        try {
            RLock lock = staticRedissonClient.getLock(lockKey);
            return lock.tryLock(waitTime, leaseTime, unit);
        } catch (Exception e) {
            log.error("尝试获取锁失败, lockKey: {}", lockKey, e);
            return false;
        }
    }

    /**
     * 释放锁
     *
     * @param lockKey 锁的key
     */
    public void unlock(String lockKey) {
        try {
            RLock lock = staticRedissonClient.getLock(lockKey);
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        } catch (Exception e) {
            log.error("释放锁失败, lockKey: {}", lockKey, e);
        }
    }

    // ============================原子操作=============================

    /**
     * 获取原子长整型
     *
     * @param key 键
     * @return 原子长整型对象
     */
    public RAtomicLong getAtomicLong(String key) {
        return staticRedissonClient.getAtomicLong(key);
    }

    /**
     * 设置原子长整型的值
     *
     * @param key   键
     * @param value 值
     */
    public void setAtomicLong(String key, long value) {
        try {
            staticRedissonClient.getAtomicLong(key).set(value);
        } catch (Exception e) {
            log.error("设置原子长整型失败, key: {}, value: {}", key, value, e);
        }
    }

    /**
     * 获取原子长整型的值
     *
     * @param key 键
     * @return 值
     */
    public long getAtomicLongValue(String key) {
        try {
            return staticRedissonClient.getAtomicLong(key).get();
        } catch (Exception e) {
            log.error("获取原子长整型值失败, key: {}", key, e);
            return 0;
        }
    }

    // ============================批量操作=============================

    /**
     * 批量获取
     *
     * @param keys 键集合
     * @return 值的Map
     */
    public Map<String, Object> multiGet(Collection<String> keys) {
        try {
            Map<String, Object> result = new HashMap<>();
            RBatch batch = staticRedissonClient.createBatch();

            for (String key : keys) {
                batch.getBucket(key).getAsync();
            }

            BatchResult<?> batchResult = batch.execute();
            List<?> responses = batchResult.getResponses();

            int index = 0;
            for (String key : keys) {
                if (index < responses.size()) {
                    result.put(key, responses.get(index));
                }
                index++;
            }

            return result;
        } catch (Exception e) {
            log.error("批量获取失败, keys: {}", keys, e);
            return Collections.emptyMap();
        }
    }

    /**
     * 批量设置
     *
     * @param keyValueMap 键值对Map
     * @return 是否成功
     */
    public boolean multiSet(Map<String, Object> keyValueMap) {
        try {
            RBatch batch = staticRedissonClient.createBatch();

            for (Map.Entry<String, Object> entry : keyValueMap.entrySet()) {
                batch.getBucket(entry.getKey()).setAsync(entry.getValue());
            }

            batch.execute();
            return true;
        } catch (Exception e) {
            log.error("批量设置失败, keyValueMap: {}", keyValueMap, e);
            return false;
        }
    }
}

