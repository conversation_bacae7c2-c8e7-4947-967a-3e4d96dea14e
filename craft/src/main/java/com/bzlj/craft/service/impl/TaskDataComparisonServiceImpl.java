package com.bzlj.craft.service.impl;

import com.bzlj.craft.command.DataPointSearchCommand;
import com.bzlj.craft.dto.*;
import com.bzlj.craft.entity.ProductionTask;
import com.bzlj.craft.enums.PointMethodType;
import com.bzlj.craft.service.ISurveillanceService;
import com.bzlj.craft.service.ITaskDataComparisonService;
import com.bzlj.craft.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.ZoneId;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

/**
 * 任务数据对比服务实现类
 * 使用虚拟线程并行查询多个任务的执行工步和工艺参数
 *
 * <AUTHOR>
 * @description: 任务数据对比服务
 * @date 2025-06-10 14:07
 */
@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class TaskDataComparisonServiceImpl implements ITaskDataComparisonService {

    private final ISurveillanceService surveillanceService;
    private final Executor virtualThreadExecutor;

    public TaskDataComparisonServiceImpl(ISurveillanceService surveillanceService,
                                         @Qualifier("virtualThreadExecutor") Executor virtualThreadExecutor) {
        this.surveillanceService = surveillanceService;
        this.virtualThreadExecutor = virtualThreadExecutor;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<TaskDetailDTO> paramComparison(List<String> taskIds) {
        log.debug("开始并行查询任务数据，任务数量: {}", taskIds.size());
        // 为每个任务创建异步查询任务
        List<CompletableFuture<TaskDetailDTO>> futures = taskIds.stream()
                .map(taskId -> CompletableFuture.supplyAsync(() -> {
                    ProductionTask task = surveillanceService.findByTaskIdWithProcess(taskId);
                    List<ResultParamsDTO> resultParams = surveillanceService.findResultParams(task.getTaskId(),task.getProcess().getId());

                    return buildTaskDetail(resultParams,taskId);
                }, virtualThreadExecutor))
                .toList();

        // 等待所有查询完成并收集结果

        return futures.stream()
                .map(CompletableFuture::join)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }


    private TaskDetailDTO buildTaskDetail(List<ResultParamsDTO> resultParams, String taskId) {
        TaskDetailDTO taskDetailDTO = new TaskDetailDTO();
        taskDetailDTO.setTaskId(taskId);
        Map<String, List<ResultParamsDTO>> map = resultParams.stream().collect(Collectors.groupingBy(ResultParamsDTO::getWorkStepName));
        List<StepDetailDTO> stepDetails = map.keySet().parallelStream().map(workStepName -> {
            StepDetailDTO stepDetailDTO = new StepDetailDTO();
            stepDetailDTO.setStepCode(map.get(workStepName).getFirst().getStepCode());
            stepDetailDTO.setStepName(workStepName);
            stepDetailDTO.setStepOrder(map.get(workStepName).getFirst().getStepOrder());
            List<ResultParamsDTO> resultParamsDTOS = map.get(workStepName);
            List<ParamDetailDTO> paramDetails = resultParamsDTOS.stream().map(resultParamsDTO -> {
                ParamDetailDTO paramDetailDTO = new ParamDetailDTO();
                paramDetailDTO.setParamName(resultParamsDTO.getStepParam());
                paramDetailDTO.setValue(resultParamsDTO.getActualValue());
                paramDetailDTO.setMax(resultParamsDTO.getMax());
                paramDetailDTO.setMin(resultParamsDTO.getMin());
                return paramDetailDTO;
            }).collect(Collectors.toList());
            stepDetailDTO.setParamDetails(paramDetails);
            return stepDetailDTO;
        }).sorted(Comparator.comparing(StepDetailDTO::getStepOrder)).collect(Collectors.toList());
        taskDetailDTO.setStepDetails(stepDetails);
        return taskDetailDTO;
    }

    @Override
    public List<ContinuousStepDTO> continuousParams(String taskId) {
        ProductionTask task = surveillanceService.findByTaskIdWithProcess(taskId);
        return surveillanceService.findContinuousParams(taskId, task.getProcess().getId());
    }

    @Override
    public Map<String, List<DataPointInfoDTO>> getContinuousData(DataPointSearchCommand command, List<String> taskIds, String baseTaskId, PointMethodType pointMethodType) {
        List<CompletableFuture<DataPointInfoDTO>> futures = taskIds.stream()
                .map(taskId -> CompletableFuture.supplyAsync(() -> {
                    DataPointSearchCommand dataPointSearchCommand = new DataPointSearchCommand();
                    BeanUtils.copyProperties(command, dataPointSearchCommand);
                    ProductionTask task = surveillanceService.findByTaskIdWithProcess(taskId);
                    command.setStart(task.getStartTime().atZone(ZoneId.systemDefault()).toInstant().getEpochSecond());
                    command.setStop(task.getEndTime().atZone(ZoneId.systemDefault()).toInstant().getEpochSecond());
                    Map<String,Object> result = (Map<String, Object>) surveillanceService.fetchPointData(command, pointMethodType);

                    List<ResultParamsDTO> resultParams = surveillanceService.findResultParams(task.getTaskId(),task.getProcess().getId());

                    return buildTaskDetail(resultParams,taskId);
                }, virtualThreadExecutor))
                .toList();
        return Map.of();
    }

    private Map<String,List<DataPointInfoDTO>> buildTaskDetail(Map<String,> result, String taskId) {
        ((Map)result)
    }

}
