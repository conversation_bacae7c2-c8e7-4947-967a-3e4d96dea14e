package com.bzlj.message.internaldistribution.service.impl;

import com.bzlj.message.internaldistribution.entity.mongo.DailyMessageStatistics;
import com.bzlj.message.internaldistribution.repository.mongo.DailyMessageStatisticsRepository;
import com.bzlj.message.internaldistribution.service.MessageStatisticsService;
import com.bzlj.message.internaldistribution.util.RedisUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * 消息统计服务实现
 *
 * <AUTHOR>
 * @date 2025/6/20
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MessageStatisticsServiceImpl implements MessageStatisticsService {

    private static final String REDIS_KEY_PREFIX = "message:statistics:";
    private static final String MESSAGE_CENTER_SUFFIX = ":message_center";
    private static final String XBUS_SUFFIX = ":xbus";
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    private final DailyMessageStatisticsRepository statisticsRepository;

    @Override
    public void incrementMessageCenterCount(String serviceId) {
        LocalDate date = LocalDate.now();
        String key = buildRedisKey(date, normalizeServiceId(serviceId), MESSAGE_CENTER_SUFFIX);
        try {
            long count = RedisUtils.incr(key);
            log.debug("消息中心计数递增成功 - 服务ID: {}, 日期: {}, 新计数: {}",
                    serviceId, date, count);
        } catch (Exception e) {
            log.error("消息中心计数递增失败 - 服务ID: {}, 日期: {}",
                    serviceId, date, e);
        }
    }

    /**
     * 构建Redis键
     */
    private String buildRedisKey(LocalDate date, String serviceId, String suffix) {
        return REDIS_KEY_PREFIX + date.format(DATE_FORMATTER) + ":" + serviceId + suffix;
    }

    /**
     * 标准化服务ID
     */
    private static String normalizeServiceId(String serviceId) {
        return (serviceId == null || serviceId.trim().isEmpty()) ? "unknown" : serviceId.trim();
    }

    @Override
    public void incrementXBusMessageCount(String serviceId) {
        LocalDate date = LocalDate.now();
        String key = buildRedisKey(date, normalizeServiceId(serviceId), XBUS_SUFFIX);
        try {
            long count = RedisUtils.incr(key);
            log.debug("XBus消息计数递增成功 - 服务ID: {}, 日期: {}, 新计数: {}",
                    serviceId, date, count);
        } catch (Exception e) {
            log.error("XBus消息计数递增失败 - 服务ID: {}, 日期: {}",
                    serviceId, date, e);
        }
    }

    @Override
    public Map<String, Long> getTodayStatistics(String serviceId) {
        LocalDate date = LocalDate.now();
        Map<String, Long> statistics = new HashMap<>();
        String normalizedServiceId = normalizeServiceId(serviceId);

        try {
            String messageCenterKey = buildRedisKey(date, normalizedServiceId, MESSAGE_CENTER_SUFFIX);
            String xbusKey = buildRedisKey(date, normalizedServiceId, XBUS_SUFFIX);

            Object messageCenterCount = RedisUtils.get(messageCenterKey);
            Object xbusCount = RedisUtils.get(xbusKey);

            statistics.put("messageCenterCount",
                    messageCenterCount != null ? Long.parseLong(messageCenterCount.toString()) : 0L);
            statistics.put("xbusMessageCount",
                    xbusCount != null ? Long.parseLong(xbusCount.toString()) : 0L);

            log.debug("获取统计数据成功 - 服务ID: {}, 日期: {}, 统计数据: {}",
                    serviceId, date, statistics);
        } catch (Exception e) {
            log.error("获取统计数据失败 - 服务ID: {}, 日期: {}", serviceId, date, e);
            statistics.put("messageCenterCount", 0L);
            statistics.put("xbusMessageCount", 0L);
        }

        return statistics;
    }

    @Override
    public Map<String, Map<String, Long>> getAllTodayStatistics() {
        LocalDate date = LocalDate.now();
        return getAllStatistics(date);
    }

    /**
     * 从Redis键中提取服务ID
     */
    private String extractServiceIdFromKey(String key) {
        try {
            // 格式: message:statistics:yyyy-MM-dd:serviceId:type
            String[] parts = key.split(":");
            if (parts.length >= 5) {
                return parts[3]; // serviceId部分
            }
        } catch (Exception e) {
            log.warn("从Redis键中提取服务ID失败: {}", key, e);
        }
        return null;
    }

    /**
     * 从Redis键中提取类型
     */
    private String extractTypeFromKey(String key) {
        try {
            if (key.endsWith(MESSAGE_CENTER_SUFFIX)) {
                return "messageCenterCount";
            } else if (key.endsWith(XBUS_SUFFIX)) {
                return "xbusMessageCount";
            }
        } catch (Exception e) {
            log.warn("从Redis键中提取类型失败: {}", key, e);
        }
        return null;
    }

    @Override
    public void saveStatisticsAndClearCache(LocalDate date) {
        log.info("开始保存统计数据并清理缓存，日期: {}", date);

        try {
            Map<String, Map<String, Long>> allStatistics = getAllStatistics(date);

            for (Map.Entry<String, Map<String, Long>> entry : allStatistics.entrySet()) {
                String serviceId = entry.getKey();
                Map<String, Long> statistics = entry.getValue();

                Long messageCenterCount = statistics.getOrDefault("messageCenterCount", 0L);
                Long xbusMessageCount = statistics.getOrDefault("xbusMessageCount", 0L);
                Long totalCount = messageCenterCount + xbusMessageCount;

                // 保存到MongoDB
                DailyMessageStatistics dailyStats = statisticsRepository
                        .findByStatisticsDateAndServiceId(date, serviceId)
                        .orElse(new DailyMessageStatistics()
                                .setStatisticsDate(date)
                                .setServiceId(serviceId)
                                .setCreateTime(LocalDateTime.now()));

                dailyStats.setMessageCenterCount(messageCenterCount)
                        .setXbusMessageCount(xbusMessageCount)
                        .setTotalCount(totalCount)
                        .setUpdateTime(LocalDateTime.now());

                statisticsRepository.save(dailyStats);
                log.info("已保存服务统计数据 - 服务ID: {}, 日期: {}, 消息中心: {}, XBus: {}, 总计: {}",
                        serviceId, date, messageCenterCount, xbusMessageCount, totalCount);
            }

            // 清理Redis缓存
            clearStatistics(date);
            log.info("统计数据保存和缓存清理完成，日期: {}", date);

        } catch (Exception e) {
            log.error("统计数据保存和缓存清理失败，日期: {}", date, e);
        }
    }

    private Map<String, Map<String, Long>> getAllStatistics(LocalDate date) {
        Map<String, Map<String, Long>> allStatistics = new HashMap<>();
        String pattern = buildRedisKey(date, "*", "*");

        try {
            Set<String> keys = RedisUtils.keys(pattern);
            for (String key : keys) {
                String serviceId = extractServiceIdFromKey(key);
                String type = extractTypeFromKey(key);

                if (serviceId != null && type != null) {
                    Object count = RedisUtils.get(key);
                    Long countValue = count != null ? Long.valueOf(count.toString()) : 0L;

                    allStatistics.computeIfAbsent(serviceId, k -> new HashMap<>()).put(type, countValue);
                }
            }

            log.debug("获取所有统计数据成功，日期: {}, 结果: {}", date, allStatistics);
        } catch (Exception e) {
            log.error("获取所有统计数据失败，日期: {}", date, e);
        }

        return allStatistics;
    }

    @Override
    public void initTodayCache() {
        LocalDate today = LocalDate.now();
        log.info("初始化今日缓存，日期: {}", today);

        try {
            // 检查是否已有缓存，如果没有则初始化
            boolean hasCache = hasStatistics(today);

            if (!hasCache) {
                log.info("今日暂无缓存数据，将在首次访问时创建");
            } else {
                Set<String> serviceIds = getServiceIds(today);
                log.info("发现今日已有 {} 个服务的缓存数据", serviceIds.size());
            }

        } catch (Exception e) {
            log.error("初始化今日缓存失败", e);
        }
    }

    /**
     * 清理指定日期的所有统计缓存
     *
     * @param date 日期
     * @return 清理的键数量
     */
    public long clearStatistics(LocalDate date) {
        String pattern = buildRedisKey(date, "*", "*");

        try {
            long deletedCount = RedisUtils.deleteByPattern(pattern);
            log.info("已清理 {} 个统计缓存键，日期: {}", deletedCount, date);
            return deletedCount;
        } catch (Exception e) {
            log.error("清理统计缓存失败，日期: {}", date, e);
            return 0;
        }
    }

    /**
     * 清理指定服务指定日期的统计缓存
     *
     * @param serviceId 服务ID
     * @param date      日期
     * @return 清理的键数量
     */
    public long clearServiceStatistics(String serviceId, LocalDate date) {
        String normalizedServiceId = normalizeServiceId(serviceId);
        String pattern = buildRedisKey(date, normalizedServiceId, "*");

        try {
            long deletedCount = RedisUtils.deleteByPattern(pattern);
            log.info("已清理 {} 个统计缓存键 - 服务ID: {}, 日期: {}",
                    deletedCount, serviceId, date);
            return deletedCount;
        } catch (Exception e) {
            log.error("清理服务统计缓存失败 - 服务ID: {}, 日期: {}", serviceId, date, e);
            return 0;
        }
    }

    /**
     * 检查指定日期是否有统计数据
     *
     * @param date 日期
     * @return 是否有数据
     */
    public boolean hasStatistics(LocalDate date) {
        String pattern = buildRedisKey(date, "*", "*");

        try {
            Set<String> keys = RedisUtils.keys(pattern);
            return keys != null && !keys.isEmpty();
        } catch (Exception e) {
            log.error("检查统计数据是否存在失败，日期: {}", date, e);
            return false;
        }
    }

    /**
     * 获取指定日期的服务ID列表
     *
     * @param date 日期
     * @return 服务ID集合
     */
    public Set<String> getServiceIds(LocalDate date) {
        String pattern = buildRedisKey(date, "*", "*");

        try {
            Set<String> keys = RedisUtils.keys(pattern);
            return keys.stream()
                    .map(this::extractServiceIdFromKey)
                    .filter(serviceId -> serviceId != null && !serviceId.isEmpty())
                    .collect(java.util.stream.Collectors.toSet());
        } catch (Exception e) {
            log.error("获取服务ID列表失败，日期: {}", date, e);
            return Set.of();
        }
    }
}
