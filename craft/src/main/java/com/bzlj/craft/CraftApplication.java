package com.bzlj.craft;

import com.bici.common.security.handler.GlobalExceptionHandler;
import com.bzlj.dynamic.mongo.annotations.EnableDynamicMongo;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.quartz.QuartzAutoConfiguration;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.data.mongodb.repository.config.EnableMongoRepositories;
import org.springframework.web.socket.config.annotation.EnableWebSocket;

@EnableMongoRepositories(basePackages = {"com.bzlj.craft.mongo.repository"})
@SpringBootApplication(exclude = {QuartzAutoConfiguration.class})
@EnableDynamicMongo
@EnableFeignClients(value = {"com.bzlj.craft.*","com.bici.system.api"})
@EnableWebSocket
@EnableJpaAuditing
@ComponentScan(basePackages = {"com.bzlj.*", "com.bici.*","bici.bzlj.*"}, excludeFilters = {@ComponentScan.Filter(
        type = FilterType.ASSIGNABLE_TYPE,
        classes = GlobalExceptionHandler.class
)})
public class CraftApplication {
    public static void main(String[] args) {
        SpringApplication.run(CraftApplication.class, args);
    }
}