<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>bici.bzlj</groupId>
        <artifactId>bzlj</artifactId>
        <version>${bzlj.version}</version>
    </parent>
    <packaging>jar</packaging>
    <artifactId>craft</artifactId>

    <properties>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.dameng</groupId>
            <artifactId>DmJdbcDriver8</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.bzlj</groupId>
            <artifactId>dynamic-mongo-spring-boot-starter</artifactId>
            <version>${dynamic.mongo.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-webflux</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-test</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-test-autoconfigure</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>

        <!-- https://mvnrepository.com/artifact/com.dameng/DmDialect-for-hibernate6.6 -->
        <dependency>
            <groupId>com.dameng</groupId>
            <artifactId>DmDialect-for-hibernate6.6</artifactId>
        </dependency>
        <dependency>
            <groupId>org.hibernate</groupId>
            <artifactId>hibernate-core</artifactId>
        </dependency>

        <dependency>
            <groupId>org.reflections</groupId>
            <artifactId>reflections</artifactId>
            <version>${reflections.version}</version>
        </dependency>

        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>

        <dependency>
            <groupId>com.bici</groupId>
            <artifactId>graph-ops-nebula</artifactId>
        </dependency>
        <!--注意引入顺序，db-migration 必须在前面先引入。-->
        <dependency>
            <groupId>com.github.mengweijin</groupId>
            <artifactId>db-migration</artifactId>
            <version>${db-migration}</version>
        </dependency>
        <!--flyway 的版本一般不需要指定（会使用 spring boot 默认的版本），如果兼容 spring boot 2.5 和 2.4 版本，则需要明确指定为 7.15.0 版本。-->
        <dependency>
            <artifactId>flyway-core</artifactId>
            <groupId>org.flywaydb</groupId>
            <version>10.10.0</version>
        </dependency>
        <dependency>
            <groupId>com.querydsl</groupId>
            <artifactId>querydsl-jpa</artifactId>
            <!--            <version>${querydsl.version}</version>-->
            <classifier>jakarta</classifier>
        </dependency>

        <dependency>
            <groupId>com.blazebit</groupId>
            <artifactId>blaze-persistence-integration-querydsl-expressions</artifactId>
            <!--            <version>${blazebit.version}</version>-->
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>com.blazebit</groupId>-->
        <!--            <artifactId>blaze-persistence-integration-hibernate-5.3</artifactId>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>com.blazebit</groupId>-->
        <!--            <artifactId>blaze-persistence-core-api</artifactId>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>com.blazebit</groupId>-->
        <!--            <artifactId>blaze-persistence-core-impl</artifactId>-->
        <!--        </dependency>-->

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <!--            <version>${hutool.version}</version>-->
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-api</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- https://mvnrepository.com/artifact/io.reactivex.rxjava3/rxjava -->
        <dependency>
            <groupId>io.reactivex.rxjava3</groupId>
            <artifactId>rxjava</artifactId>
            <version>3.1.8</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-mongodb</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
            <version>${spring-cloud.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-websocket</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-stream-kafka</artifactId>
            <version>${spring-cloud.version}</version>
        </dependency>

        <dependency>
            <groupId>org.testng</groupId>
            <artifactId>testng</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <version>4.0.0</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>bici.bzlj</groupId>
            <artifactId>craft-common</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>4.1.2</version>
        </dependency>

        <dependency>
            <groupId>com.github.ben-manes.caffeine</groupId>
            <artifactId>caffeine</artifactId>
        </dependency>

        <dependency>
            <groupId>bici.bzlj</groupId>
            <artifactId>core</artifactId>
            <version>1.0.0</version>
        </dependency>

    </dependencies>
    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <finalName>CraftApplication</finalName>
                    <layout>ZIP</layout>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>pl.project13.maven</groupId>
                <artifactId>git-commit-id-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>revision</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>com.google.cloud.tools</groupId>
                <artifactId>jib-maven-plugin</artifactId>
                <version>3.4.4</version>
                <executions>
                    <execution>
                        <phase>${jib.phase.backed}</phase>
                        <goals>
                            <goal>build</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <!-- 拉取所需的基础镜像 - 这里用于运行springboot项目 -->
                    <from>
                        <image>***************/public/alpine-openjdk21-jre:latest</image>
                        <auth>
                            <username>admin</username>
                            <password>Changeme_123</password>
                        </auth>
                    </from>

                    <!-- 最后生成的镜像配置 -->
                    <to>
                        <!--                        <image>${harbor.host}/${harbor.namespace}-${docker.repository}/${artifactId}</image>-->
                        <image>${harbor.host}/${harbor.namespace}/bwty-${project.artifactId}</image>
                        <!-- 镜像版本号 -->
                        <tags>
<!--                            <tag>${git.branch}</tag>-->
                            <tag>${maven.build.timestamp}</tag>
                        </tags>
                        <auth>
                            <username>${harbor.username}</username>
                            <password>${harbor.password}</password>
                        </auth>
                    </to>

                    <container>
                        <!--镜像创建时间改成为当前时间-->
                        <creationTime>USE_CURRENT_TIMESTAMP</creationTime>
                        <!--入口主类-->
                        <mainClass>com.bzlj.craft.CraftApplication</mainClass>
                        <!--配置使用的时区-->
                        <environment>
                            <TZ>Asia/Shanghai</TZ>
                        </environment>
                        <labels>
                            <git.commit.user.name>${git.commit.user.name}</git.commit.user.name>
                            <git.commit.time>${git.commit.time}</git.commit.time>
                            <git.branch>${git.branch}</git.branch>
                            <git.commit.id>${git.commit.id}</git.commit.id>
                        </labels>
                        <appRoot>/opt/apps</appRoot>
                    </container>
                    <extraDirectories>
                        <paths>
                            <path>
                                <from>../staticResource/</from>
                                <into>/opt/craft/data/static-resource/</into>
                            </path>
                        </paths>
                    </extraDirectories>
                    <allowInsecureRegistries>true</allowInsecureRegistries>
                </configuration>
            </plugin>

            <plugin>
                <groupId>com.mysema.maven</groupId>
                <artifactId>apt-maven-plugin</artifactId>
                <version>1.1.3</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>process</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>target/generated-sources/java</outputDirectory>
                            <processor>com.querydsl.apt.jpa.JPAAnnotationProcessor</processor>
                        </configuration>
                    </execution>
                </executions>
                <dependencies>
                    <dependency>
                        <groupId>com.querydsl</groupId>
                        <artifactId>querydsl-apt</artifactId>
                        <version>${querydsl.version}</version>
                        <classifier>jakarta</classifier>
                    </dependency>
                </dependencies>
            </plugin>
        </plugins>
    </build>
    <repositories>
        <repository>
            <id>bici</id>
            <name>bici nexus</name>
            <url>https://nexus.bicisims.com/repository/maven-public/</url>
        </repository>
    </repositories>
    <pluginRepositories>
        <pluginRepository>
            <id>bici</id>
            <name>bici nexus</name>
            <url>https://nexus.bicisims.com/repository/maven-public/</url>
        </pluginRepository>
    </pluginRepositories>
    <profiles>

        <!-- 单机版 默认就是这个版本 -->
        <profile>
            <id>dev</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <profiles.active>dev</profiles.active>
                <docker.repository>dev</docker.repository>
                <harbor.host>***************:30003</harbor.host>
                <jib.user.name>bzlj</jib.user.name>
                <jib.user.password>Changeme_123</jib.user.password>
            </properties>


            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-compiler-plugin</artifactId>
                        <configuration>
                            <source>${java.version}</source>
                            <target>${java.version}</target>
                            <encoding>${project.build.sourceEncoding}</encoding>
                            <annotationProcessorPaths>
                                <path>
                                    <groupId>org.projectlombok</groupId>
                                    <artifactId>lombok</artifactId>
                                    <version>${lombok.version}</version>
                                </path>
                                <path>
                                    <groupId>io.github.linpeilie</groupId>
                                    <artifactId>mapstruct-plus-processor</artifactId>
                                    <version>${mapstruct.plus.version}</version>
                                </path>
                                <path>
                                    <groupId>org.projectlombok</groupId>
                                    <artifactId>lombok-mapstruct-binding</artifactId>
                                    <version>0.2.0</version>
                                </path>
                            </annotationProcessorPaths>
                        </configuration>
                    </plugin>
                    <plugin>
                        <artifactId>maven-clean-plugin</artifactId>
                        <configuration>
                            <filesets>
                                <fileset>
                                    <directory>src/main/resources/static</directory>
                                    <includes>
                                        <include>**</include>
                                    </includes>
                                    <followSymlinks>false</followSymlinks>
                                </fileset>
                            </filesets>
                        </configuration>
                    </plugin>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-antrun-plugin</artifactId>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>upload</id>
            <build>
                <plugins>
                    <plugin>
                        <!--打包时去除第三方依赖-->
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-maven-plugin</artifactId>
                        <configuration>
                            <layout>ZIP</layout>
                            <includes>
                                <include>
                                    <groupId>non-exists</groupId>
                                    <artifactId>non-exists</artifactId>
                                </include>
                            </includes>
                        </configuration>
                    </plugin>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-dependency-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>copy-dependencies</id>
                                <phase>package</phase>
                                <goals>
                                    <goal>copy-dependencies</goal>
                                </goals>
                                <configuration>
                                    <!--target/libs是依赖jar包的输出目录，根据自己喜好配置-->
                                    <outputDirectory>target/libs</outputDirectory>
                                    <excludeTransitive>false</excludeTransitive>
                                    <stripVersion>false</stripVersion>
                                    <includeScope>runtime</includeScope>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
</project>