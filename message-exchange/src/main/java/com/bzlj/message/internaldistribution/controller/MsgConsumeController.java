package com.bzlj.message.internaldistribution.controller;

import com.bzlj.message.internaldistribution.service.MsgConsumeService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 消息分发处理
 *
 * <AUTHOR>
 * @date 2025/5/12 9:47
 */
@Slf4j
@RestController
@RequestMapping("/consume")
@RequiredArgsConstructor
public class MsgConsumeController {

    private final MsgConsumeService msgConsumeService;


    /**
     * 消息中心消息分发处理
     *
     * @param inJsonString
     * @return
     */
    @PostMapping("/consumeMessage")
    public String consumeMessage(@RequestBody String inJsonString) {
        return msgConsumeService.consumeMessage(inJsonString);
    }

    /**
     * XBus消息分发处理
     * <p>
     * 0成功
     * 1001~1999失败返回值需要重发区间
     * 1001因内部处理原因造成失败，需要重发
     * 1002同步服务调用失败、超时
     * 1003目标服务未正常启动、目标服务返回的消息、格式无效
     * 2001~2999失败返回值无需重发区间
     * 2001代理服务号为空，无需重发
     *
     * @param inJsonString
     * @param request
     * @param response
     * @return
     */
    @PostMapping("/consumeXBusMessage")
    public String consumeXBusMessage(@RequestBody String inJsonString, HttpServletRequest request, HttpServletResponse response) {
        response.setHeader("returnCode", "0");
        response.setHeader("Content-Type", "application/json");
        return msgConsumeService.consumeXBusMessage(inJsonString, request.getHeader("serviceId"), response);
    }

}
