package com.bzlj.craft.service;

import com.bzlj.craft.command.DataPointSearchCommand;
import com.bzlj.craft.dto.ContinuousStepDTO;
import com.bzlj.craft.dto.DataPointInfoDTO;
import com.bzlj.craft.dto.TaskDetailDTO;
import com.bzlj.craft.enums.PointMethodType;

import java.util.List;
import java.util.Map;

/**
 * 任务对比
 * <AUTHOR>
 * @description:
 * @date 2025-06-10 14:04
 */
public interface ITaskDataComparisonService {
    /**
     * 参数对比
     * @param taskIds
     * @return
     */
    List<TaskDetailDTO> paramComparison(List<String> taskIds);

    /**
     * 获取连续型工艺参数
     * @param taskId
     * @return
     */
    List<ContinuousStepDTO> continuousParams(String taskId);

    List<Map<String,List<DataPointInfoDTO>>> getContinuousData(DataPointSearchCommand command, List<String> taskIds, String baseTaskId, PointMethodType pointMethodType);

}
