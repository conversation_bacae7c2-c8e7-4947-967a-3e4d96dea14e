package com.bzlj.message.internaldistribution.dto;

import cn.hutool.core.util.ObjUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 消息发送数据传输对象
 *
 * <AUTHOR>
 * @date 2025/5/16 16:52
 */
@Accessors(chain = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MessageSendDTO {

    /**
     * 服务ID/电文号
     */
    private String id;

    /**
     * 消息体
     */
    private String payload;

    /**
     * 接收时间戳
     */
    private Long receiptTime;

    /**
     * 发送时间戳
     */
    private Long sendTime;

    /**
     * 二级分类
     */
    private String type;

    @Override
    public String toString() {
        String str = "{" + "\"id\":\"" + id + "\"" + ",\"payload\":" + payload + ", \"receiptTime\":\"" + receiptTime + "\", \"type\":\"" + type + "\"  }";
        if (ObjUtil.isNotNull(sendTime))
            str = "{" + "\"id\":\"" + id + "\"" + ",\"payload\":" + payload + ", \"receiptTime\":\"" + receiptTime + "\" ,\"type\":\"" + type + "\" ,\"sendTime\":\"" + sendTime + "\" }";
        return str;
    }

}
