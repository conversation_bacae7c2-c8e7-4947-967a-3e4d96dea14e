package com.bzlj.craft.service.impl;

import com.bzlj.craft.command.DataPointSearchCommand;
import com.bzlj.craft.dto.*;
import com.bzlj.craft.entity.ProductionTask;
import com.bzlj.craft.enums.PointMethodType;
import com.bzlj.craft.service.ISurveillanceService;
import com.bzlj.craft.service.ITaskDataComparisonService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

/**
 * 任务数据对比服务实现类
 * 使用虚拟线程并行查询多个任务的执行工步和工艺参数
 *
 * <AUTHOR>
 * @description: 任务数据对比服务
 * @date 2025-06-10 14:07
 */
@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class TaskDataComparisonServiceImpl implements ITaskDataComparisonService {

    private final ISurveillanceService surveillanceService;
    private final Executor virtualThreadExecutor;

    public TaskDataComparisonServiceImpl(ISurveillanceService surveillanceService,
                                         @Qualifier("virtualThreadExecutor") Executor virtualThreadExecutor) {
        this.surveillanceService = surveillanceService;
        this.virtualThreadExecutor = virtualThreadExecutor;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<TaskDetailDTO> paramComparison(List<String> taskIds) {
        log.debug("开始并行查询任务数据，任务数量: {}", taskIds.size());
        // 为每个任务创建异步查询任务
        List<CompletableFuture<TaskDetailDTO>> futures = taskIds.stream()
                .map(taskId -> CompletableFuture.supplyAsync(() -> {
                    ProductionTask task = surveillanceService.findByTaskIdWithProcess(taskId);
                    List<ResultParamsDTO> resultParams = surveillanceService.findResultParams(task.getTaskId(),task.getProcess().getId());

                    return buildTaskDetail(resultParams,taskId);
                }, virtualThreadExecutor))
                .toList();

        // 等待所有查询完成并收集结果

        return futures.stream()
                .map(CompletableFuture::join)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }


    private TaskDetailDTO buildTaskDetail(List<ResultParamsDTO> resultParams, String taskId) {
        TaskDetailDTO taskDetailDTO = new TaskDetailDTO();
        taskDetailDTO.setTaskId(taskId);
        Map<String, List<ResultParamsDTO>> map = resultParams.stream().collect(Collectors.groupingBy(ResultParamsDTO::getWorkStepName));
        List<StepDetailDTO> stepDetails = map.keySet().parallelStream().map(workStepName -> {
            StepDetailDTO stepDetailDTO = new StepDetailDTO();
            stepDetailDTO.setStepCode(map.get(workStepName).getFirst().getStepCode());
            stepDetailDTO.setStepName(workStepName);
            stepDetailDTO.setStepOrder(map.get(workStepName).getFirst().getStepOrder());
            List<ResultParamsDTO> resultParamsDTOS = map.get(workStepName);
            List<ParamDetailDTO> paramDetails = resultParamsDTOS.stream().map(resultParamsDTO -> {
                ParamDetailDTO paramDetailDTO = new ParamDetailDTO();
                paramDetailDTO.setParamName(resultParamsDTO.getStepParam());
                paramDetailDTO.setValue(resultParamsDTO.getActualValue());
                paramDetailDTO.setMax(resultParamsDTO.getMax());
                paramDetailDTO.setMin(resultParamsDTO.getMin());
                return paramDetailDTO;
            }).collect(Collectors.toList());
            stepDetailDTO.setParamDetails(paramDetails);
            return stepDetailDTO;
        }).sorted(Comparator.comparing(StepDetailDTO::getStepOrder)).collect(Collectors.toList());
        taskDetailDTO.setStepDetails(stepDetails);
        return taskDetailDTO;
    }

    @Override
    public List<ContinuousStepDTO> continuousParams(String taskId) {
        ProductionTask task = surveillanceService.findByTaskIdWithProcess(taskId);
        return surveillanceService.findContinuousParams(taskId, task.getProcess().getId());
    }

    @Override
    public Map<String, List<DataPointInfoDTO>> getContinuousData(DataPointSearchCommand command, List<String> taskIds, String baseTaskId, PointMethodType pointMethodType) {
        log.info("开始获取连续数据，任务数量: {}, 基准任务: {}", taskIds.size(), baseTaskId);
        long startTime = System.currentTimeMillis();

        try {
            // 使用虚拟线程并行查询各个任务的点位数据
            List<CompletableFuture<TaskPointData>> futures = taskIds.stream()
                    .map(taskId -> CompletableFuture.supplyAsync(() -> {
                        try {
                            return queryTaskPointData(command, taskId, pointMethodType);
                        } catch (Exception e) {
                            log.error("查询任务点位数据失败，taskId: {}", taskId, e);
                            return null;
                        }
                    }, virtualThreadExecutor))
                    .collect(Collectors.toList());

            // 等待所有查询完成并收集结果
            List<TaskPointData> taskPointDataList = futures.stream()
                    .map(CompletableFuture::join)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            // 执行点位数据对齐
            Map<String, List<DataPointInfoDTO>> alignedData = alignPointDataWithBase(taskPointDataList, baseTaskId);

            long endTime = System.currentTimeMillis();
            log.info("连续数据获取完成，耗时: {}ms, 成功任务数: {}", endTime - startTime, alignedData.size());

            return alignedData;
        } catch (Exception e) {
            log.error("获取连续数据失败", e);
            throw new RuntimeException("获取连续数据失败: " + e.getMessage(), e);
        }
    }


    /**
     * 查询单个任务的点位数据
     *
     * @param command 查询命令
     * @param taskId 任务ID
     * @param pointMethodType 点位方法类型
     * @return 任务点位数据
     */
    private TaskPointData queryTaskPointData(DataPointSearchCommand command, String taskId, PointMethodType pointMethodType) {
        log.debug("开始查询任务点位数据，taskId: {}", taskId);

        // 创建独立的查询命令副本
        DataPointSearchCommand taskCommand = new DataPointSearchCommand();
        BeanUtils.copyProperties(command, taskCommand);

        // 查询任务信息
        ProductionTask task = surveillanceService.findByTaskIdWithProcess(taskId);
        if (task == null) {
            log.warn("任务不存在，taskId: {}", taskId);
            return null;
        }

        // 设置任务的时间范围
        taskCommand.setStart(task.getStartTime().atZone(ZoneId.systemDefault()).toInstant().getEpochSecond());
        taskCommand.setStop(task.getEndTime().atZone(ZoneId.systemDefault()).toInstant().getEpochSecond());

        // 查询点位数据
        Map<String, Object> result = (Map<String, Object>) surveillanceService.fetchPointData(taskCommand, pointMethodType);

        // 构建任务点位数据
        TaskPointData taskPointData = buildTaskPointData(result, taskId, task);

        log.debug("任务点位数据查询完成，taskId: {}, 点位数量: {}", taskId,
                taskPointData != null && taskPointData.getPointDataMap() != null ? taskPointData.getPointDataMap().size() : 0);

        return taskPointData;
    }

    /**
     * 构建任务点位数据
     *
     * @param result 查询结果
     * @param taskId 任务ID
     * @param task 任务实体
     * @return 任务点位数据
     */
    private TaskPointData buildTaskPointData(Map<String, Object> result, String taskId, ProductionTask task) {
        try {
            TaskPointData taskPointData = new TaskPointData();
            taskPointData.setTaskId(taskId);
            taskPointData.setTask(task);

            // 解析点位数据
            if (result != null && result.containsKey("data")) {
                Map<String, List<DataPointInfoDTO>> pointDataMap = (Map<String, List<DataPointInfoDTO>>) result.get("data");
                taskPointData.setPointDataMap(pointDataMap);

                // 计算原始时间范围
                calculateTimeRange(taskPointData);
            } else {
                taskPointData.setPointDataMap(new HashMap<>());
            }

            return taskPointData;
        } catch (Exception e) {
            log.error("构建任务点位数据失败，taskId: {}", taskId, e);
            return null;
        }
    }

    /**
     * 计算任务点位数据的时间范围
     *
     * @param taskPointData 任务点位数据
     */
    private void calculateTimeRange(TaskPointData taskPointData) {
        Long minTimestamp = null;
        Long maxTimestamp = null;

        for (List<DataPointInfoDTO> pointList : taskPointData.getPointDataMap().values()) {
            for (DataPointInfoDTO point : pointList) {
                if (point.getTs() != null) {
                    if (minTimestamp == null || point.getTs() < minTimestamp) {
                        minTimestamp = point.getTs();
                    }
                    if (maxTimestamp == null || point.getTs() > maxTimestamp) {
                        maxTimestamp = point.getTs();
                    }
                }
            }
        }

        taskPointData.setMinTimestamp(minTimestamp);
        taskPointData.setMaxTimestamp(maxTimestamp);
    }

    /**
     * 以基准任务的初始点位信息为原点，对其他任务的点位数据进行对齐
     *
     * @param taskPointDataList 所有任务的点位数据
     * @param baseTaskId 基准任务ID
     * @return 对齐后的点位数据
     */
    private Map<String, List<DataPointInfoDTO>> alignPointDataWithBase(List<TaskPointData> taskPointDataList, String baseTaskId) {
        log.info("开始执行点位数据对齐，基准任务: {}", baseTaskId);

        // 查找基准任务数据
        TaskPointData baseTaskData = taskPointDataList.stream()
                .filter(data -> baseTaskId.equals(data.getTaskId()))
                .findFirst()
                .orElse(null);

        if (baseTaskData == null) {
            log.error("基准任务数据不存在，baseTaskId: {}", baseTaskId);
            throw new RuntimeException("基准任务数据不存在: " + baseTaskId);
        }

        // 获取基准任务的初始时间戳（原点）
        Long baseOriginTimestamp = baseTaskData.getMinTimestamp();
        if (baseOriginTimestamp == null) {
            log.error("基准任务没有有效的时间戳数据，baseTaskId: {}", baseTaskId);
            throw new RuntimeException("基准任务没有有效的时间戳数据: " + baseTaskId);
        }

        log.info("基准任务原点时间戳: {}, 对应时间: {}", baseOriginTimestamp,
                Instant.ofEpochMilli(baseOriginTimestamp).atZone(ZoneId.systemDefault()).toLocalDateTime());

        Map<String, List<DataPointInfoDTO>> alignedDataMap = new HashMap<>();

        // 对每个任务的点位数据进行对齐处理
        for (TaskPointData taskData : taskPointDataList) {
            try {
                List<DataPointInfoDTO> alignedPoints = alignTaskPointData(taskData, baseOriginTimestamp);
                alignedDataMap.put(taskData.getTaskId(), alignedPoints);

                log.debug("任务点位数据对齐完成，taskId: {}, 对齐后点位数量: {}",
                        taskData.getTaskId(), alignedPoints.size());
            } catch (Exception e) {
                log.error("任务点位数据对齐失败，taskId: {}", taskData.getTaskId(), e);
                // 对齐失败时，仍然保留原始数据
                List<DataPointInfoDTO> originalPoints = taskData.getPointDataMap().values().stream()
                        .flatMap(Collection::stream)
                        .collect(Collectors.toList());
                alignedDataMap.put(taskData.getTaskId(), originalPoints);
            }
        }

        log.info("点位数据对齐完成，成功对齐任务数: {}", alignedDataMap.size());
        return alignedDataMap;
    }

    /**
     * 对单个任务的点位数据进行对齐
     *
     * @param taskData 任务点位数据
     * @param baseOriginTimestamp 基准原点时间戳
     * @return 对齐后的点位数据
     */
    private List<DataPointInfoDTO> alignTaskPointData(TaskPointData taskData, Long baseOriginTimestamp) {
        List<DataPointInfoDTO> alignedPoints = new ArrayList<>();

        // 获取当前任务的初始时间戳
        Long taskOriginTimestamp = taskData.getMinTimestamp();
        if (taskOriginTimestamp == null) {
            log.warn("任务没有有效的时间戳数据，taskId: {}", taskData.getTaskId());
            return alignedPoints;
        }

        // 计算时间偏移量（当前任务相对于基准任务的时间差）
        long timeOffset = taskOriginTimestamp - baseOriginTimestamp;

        log.debug("任务时间对齐信息 - taskId: {}, 原始起始时间: {}, 时间偏移: {}ms",
                taskData.getTaskId(),
                Instant.ofEpochMilli(taskOriginTimestamp).atZone(ZoneId.systemDefault()).toLocalDateTime(),
                timeOffset);

        // 对所有点位数据进行时间对齐
        for (Map.Entry<String, List<DataPointInfoDTO>> entry : taskData.getPointDataMap().entrySet()) {
            String pointCode = entry.getKey();
            List<DataPointInfoDTO> originalPoints = entry.getValue();

            for (DataPointInfoDTO originalPoint : originalPoints) {
                if (originalPoint.getTs() != null) {
                    // 创建对齐后的点位数据
                    DataPointInfoDTO alignedPoint = createAlignedPoint(originalPoint, baseOriginTimestamp, timeOffset);
                    alignedPoints.add(alignedPoint);
                }
            }
        }

        // 按对齐后的时间戳排序
        alignedPoints.sort(Comparator.comparing(DataPointInfoDTO::getTs));

        return alignedPoints;
    }

    /**
     * 创建对齐后的点位数据
     *
     * @param originalPoint 原始点位数据
     * @param baseOriginTimestamp 基准原点时间戳
     * @param timeOffset 时间偏移量
     * @return 对齐后的点位数据
     */
    private DataPointInfoDTO createAlignedPoint(DataPointInfoDTO originalPoint, Long baseOriginTimestamp, long timeOffset) {
        DataPointInfoDTO alignedPoint = new DataPointInfoDTO();

        // 计算对齐后的时间戳：原始时间戳 - 时间偏移量
        // 这样所有任务的起始点都会对齐到基准任务的起始时间
        Long alignedTimestamp = originalPoint.getTs() - timeOffset;

        alignedPoint.setTs(alignedTimestamp);
        alignedPoint.setV(originalPoint.getV());

        // 将时间戳转换为LocalDateTime
        if (alignedTimestamp != null) {
            alignedPoint.setTime(Instant.ofEpochMilli(alignedTimestamp)
                    .atZone(ZoneId.systemDefault())
                    .toLocalDateTime());
        }

        return alignedPoint;
    }

    /**
     * 任务点位数据内部类
     */
    private static class TaskPointData {
        private String taskId;
        private ProductionTask task;
        private Map<String, List<DataPointInfoDTO>> pointDataMap;
        private Long minTimestamp;
        private Long maxTimestamp;

        // Getters and Setters
        public String getTaskId() {
            return taskId;
        }

        public void setTaskId(String taskId) {
            this.taskId = taskId;
        }

        public ProductionTask getTask() {
            return task;
        }

        public void setTask(ProductionTask task) {
            this.task = task;
        }

        public Map<String, List<DataPointInfoDTO>> getPointDataMap() {
            return pointDataMap;
        }

        public void setPointDataMap(Map<String, List<DataPointInfoDTO>> pointDataMap) {
            this.pointDataMap = pointDataMap;
        }

        public Long getMinTimestamp() {
            return minTimestamp;
        }

        public void setMinTimestamp(Long minTimestamp) {
            this.minTimestamp = minTimestamp;
        }

        public Long getMaxTimestamp() {
            return maxTimestamp;
        }

        public void setMaxTimestamp(Long maxTimestamp) {
            this.maxTimestamp = maxTimestamp;
        }
    }

}
