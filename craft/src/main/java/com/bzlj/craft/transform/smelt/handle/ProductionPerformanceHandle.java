package com.bzlj.craft.transform.smelt.handle;

import bici.bzlj.dataprocess.core.annotation.MessageHandler;
import com.bzlj.base.search.SearchCondition;
import com.bzlj.base.search.SearchItem;
import com.bzlj.base.search.SearchItems;
import com.bzlj.craft.api.service.IMaterialService;
import com.bzlj.craft.api.service.IOperationLogService;
import com.bzlj.craft.api.service.ITaskMaterialService;
import com.bzlj.craft.entity.*;
import com.bzlj.craft.enums.DictCode;
import com.bzlj.craft.enums.MaterialAttrType;
import com.bzlj.craft.enums.TaskStatus;
import com.bzlj.craft.repository.MaterialAttrRepository;
import com.bzlj.craft.service.ISurveillanceService;
import com.bzlj.craft.service.ISysDictItemService;
import com.bzlj.craft.transform.common.CommonHandler;
import com.bzlj.craft.transform.data.DataPrepareService;
import com.bzlj.craft.util.DateTimeConverter;
import com.bzlj.craft.util.JsonUtils;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

@MessageHandler(messageType = "production_performance", desc = "生产实绩")
public class ProductionPerformanceHandle extends CommonHandler<String> {

    @Autowired
    private ISurveillanceService surveillanceService;

    @Autowired
    private DataPrepareService dataPrepareService;

    @Autowired
    private IMaterialService materialService;

    @Autowired
    private ITaskMaterialService taskMaterialService;

    @Autowired
    private ISysDictItemService sysDictItemService;

    @Autowired
    private MaterialAttrRepository materialAttrRepository;

    @Autowired
    private IOperationLogService operationLogService;

    @Transactional(rollbackFor = Exception.class)
    public void transform(String json) {
        JsonNode jsonNode = JsonUtils.toJsonNode(json);
        String taskCode = jsonNode.get("taskCode").asText();
        SearchCondition searchCondition = new SearchCondition();
        searchCondition.setSearchItems(SearchItems.builder().item(new SearchItem("taskCode", taskCode, null, SearchItem.Operator.EQ)).build());
        surveillanceService.changeStatus(taskCode, dataPrepareService.getStatusDictItem(TaskStatus.completed.getCode()));
        ProductionTask task = surveillanceService.findOne(searchCondition);
        if (Objects.nonNull(jsonNode.get("startTime"))) {
            task.setStartTime(DateTimeConverter.convertToLocalDateTime(jsonNode.get("startTime").asText()));
        }

        if (Objects.nonNull(jsonNode.get("endTime"))) {
            task.setEndTime(DateTimeConverter.convertToLocalDateTime(jsonNode.get("endTime").asText()));
        }
        //更新任务
        ProductionTask productionTask = surveillanceService.updateEntity(task);
        ArrayNode inputMaterialNodes = (ArrayNode) jsonNode.get("inputMaterials");
        //保存投入物料
        dealInputMaterials(inputMaterialNodes, productionTask);
        List<SysDictItem> attrType = sysDictItemService.findEntityByDictCode(DictCode.MATERIAL_ATTR_TYPE.getCode());
        ImmutableMap<String, SysDictItem> attrTypeMap = Maps.uniqueIndex(attrType, SysDictItem::getItemCode);
        //保存产出物料
        dealOutputMaterials((ArrayNode) jsonNode.get("outputMaterials"), productionTask, attrTypeMap);

        //保存生产实绩（操作记录）
        dealOperationLog(jsonNode.get("operationLog"), productionTask);

        //todo 保存班组信息
        dealTeamInfo(jsonNode.get("teamInfo"), productionTask);
    }

    private void dealInputMaterials(ArrayNode inputMaterialNodes, ProductionTask productionTask) {
        if (Objects.nonNull(inputMaterialNodes) && !inputMaterialNodes.isEmpty()) {
            List<String> inputMaterials = StreamSupport.stream(inputMaterialNodes.spliterator(), false)
                    .map(JsonNode::asText).distinct()
                    .collect(Collectors.toList());
            List<Material> materials = materialService.findByMaterialCodes(inputMaterials);
            List<String> existMaterials = materials.stream().map(Material::getMaterialCode).collect(Collectors.toList());
            inputMaterials.removeAll(existMaterials);
            if (!CollectionUtils.isEmpty(inputMaterials)) {
                List<Material> materialList = inputMaterials.stream().map(inputMaterial -> {
                    Material material = new Material();
                    //todo 其他属性暂无
                    material.setMaterialCode(inputMaterial);
                    material.setMaterialName(inputMaterial);
                    return material;
                }).collect(Collectors.toList());
                materials.addAll(materialService.batchInsertEntity(materialList));
            }
            //创建投入物料
            buildTaskMaterialAndSave(materials, productionTask, false);
        }
    }

    private void dealOutputMaterials(ArrayNode outputMaterialNodes, ProductionTask productionTask, Map<String, SysDictItem> attrTypeMap) {
        if (Objects.nonNull(outputMaterialNodes) && !outputMaterialNodes.isEmpty()) {
            Map<String, List<MaterialAttr>> materialAttrMap = new HashMap<>();
            List<Material> materials = new ArrayList<>();
            outputMaterialNodes.iterator().forEachRemaining(outputMaterialNode -> {
                Material material = new Material();
                material.setMaterialCode(outputMaterialNode.get("materialCode").asText());
                material.setMaterialName(outputMaterialNode.get("materialName").asText());
                material.setMaterialType("实绩");
                materials.add(material);
                List<MaterialAttr> materialAttrs = materialAttrMap.getOrDefault(material.getMaterialCode(), new ArrayList<>());
                if (Objects.nonNull(outputMaterialNode.get("physicalAttr"))) {
                    MaterialAttr materialAttr = new MaterialAttr();
                    materialAttr.setAttr(JsonUtils.toMap(outputMaterialNode.get("physicalAttr")));
                    materialAttr.setAttrType(attrTypeMap.get(MaterialAttrType.physical_attr.getCode()));
                    materialAttrs.add(materialAttr);
                    materialAttrMap.put(material.getMaterialCode(), materialAttrs);
                }
                if (Objects.nonNull(outputMaterialNode.get("specificationAttr"))) {
                    MaterialAttr materialAttr = new MaterialAttr();
                    materialAttr.setAttr(JsonUtils.toMap(outputMaterialNode.get("specificationAttr")));
                    materialAttr.setAttrType(attrTypeMap.get(MaterialAttrType.specification_attr.getCode()));
                    materialAttrs.add(materialAttr);
                    materialAttrMap.put(material.getMaterialCode(), materialAttrs);
                }
            });
            List<Material> materialList = materialService.batchInsertEntity(materials);
            if (!CollectionUtils.isEmpty(materialAttrMap)) {
                materialList.forEach(material -> {
                    List<MaterialAttr> attrs = materialAttrMap.get(material.getMaterialCode());
                    if (Objects.nonNull(attrs)) {
                        attrs.forEach(materialAttr -> {
                            materialAttr.setMaterial(material);
                        });
                    }
                });
                materialAttrRepository.saveAll(materialAttrMap.values().stream().flatMap(Collection::stream).collect(Collectors.toList()));
            }
            buildTaskMaterialAndSave(materialList, productionTask, true);

        }
    }

    /**
     * 创建并保存投入产出物料
     *
     * @param materials
     * @param productionTask
     * @param relationType
     */
    private void buildTaskMaterialAndSave(List<Material> materials, ProductionTask productionTask, Boolean relationType) {
        //创建产出物料
        List<TaskMaterial> taskMaterials = materials.stream().map(material -> {
            TaskMaterial taskMaterial = new TaskMaterial();
            taskMaterial.setMaterial(material);
            taskMaterial.setTask(productionTask);
            taskMaterial.setRelationType(relationType);
            taskMaterial.setId(new TaskMaterialId(productionTask.getTaskId(), material.getMaterialId()));
            return taskMaterial;
        }).collect(Collectors.toList());
        taskMaterialService.batchInsertEntity(taskMaterials);
    }

    private void dealOperationLog(JsonNode jsonNode, ProductionTask productionTask) {
        if (Objects.isNull(jsonNode)) return;
        OperationLog operationLog = new OperationLog();
        operationLog.setTask(productionTask);
        operationLog.setDescription(JsonUtils.toMap(jsonNode));
        operationLog.setOperationType("-");
        operationLogService.insertEntity(operationLog);
    }

    private void dealTeamInfo (JsonNode jsonNode, ProductionTask productionTask) {
        if (Objects.isNull(jsonNode)) return;

    }
}
