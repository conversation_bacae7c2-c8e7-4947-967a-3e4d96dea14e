package com.bzlj.craft.transform.smelt.handle;

import bici.bzlj.dataprocess.core.annotation.MessageHandler;
import com.bzlj.craft.enums.TaskStatus;
import com.bzlj.craft.repository.ProductionTaskExtendRepository;
import com.bzlj.craft.service.ISurveillanceService;
import com.bzlj.craft.transform.common.CommonHandler;
import com.bzlj.craft.transform.data.DataPrepareService;
import com.bzlj.craft.transform.enums.TaskChangeStatus;
import com.bzlj.craft.util.JsonUtils;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;

@Slf4j
@MessageHandler(messageType = "task_change_status", desc = "任务状态")
public class TaskChangeStatusHandle extends CommonHandler<String> {

    @Autowired
    private ISurveillanceService surveillanceService;

    @Autowired
    private ProductionTaskExtendRepository productionTaskExtendRepository;

    @Autowired
    private DataPrepareService dataPrepareService;

    @Transactional(rollbackFor = Exception.class)
    void changeTaskStatus(String json) {
        JsonNode jsonNode = JsonUtils.toJsonNode(json);
        JsonNode status = jsonNode.get("taskStatus");
        JsonNode taskCode = jsonNode.get("taskCode");
        if (Objects.isNull(status)) {
            throw new RuntimeException("任务更改状态为空");
        }
        if (Objects.isNull(taskCode)) {
            throw new RuntimeException("任务更改任务号为空");
        }
        switch (TaskChangeStatus.getByCode(status.asText())) {
            case CANCEL:
                surveillanceService.deleteTask(taskCode.asText());
                break;
            case CANNOT_CANCEL:
                log.info("{}:任务无法撤销，任务状态：{}", taskCode.asText(), status.asText());
                break;
            case START:
                surveillanceService.changeStatus(taskCode.asText(), dataPrepareService.getStatusDictItem(TaskStatus.in_progress.getCode()));
                log.info("{}:任务开始，更新任务状态：{}", taskCode.asText(), status.asText());
                break;
            default:
                log.info("{}:任务更改状态为{}，无需处理", taskCode.asText(), status.asText());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void transform(String s) {
        changeTaskStatus(s);
    }
}
