package com.bzlj.craft.controller;


import com.bzlj.base.response.UnifyResponse;
import com.bzlj.base.result.DataResult;
import com.bzlj.base.result.PageResult;
import com.bzlj.base.search.SearchCondition;
import com.bzlj.craft.component.command.ComponentRequest;
import com.bzlj.craft.component.exec.IComponentExecService;
import com.bzlj.craft.component.factory.ComponentFactory;
import com.bzlj.craft.component.result.ComponentResult;
import com.bzlj.craft.dto.ProductionTaskDTO;
import com.bzlj.craft.enums.PointMethodType;
import com.bzlj.craft.service.ISurveillanceService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.web.bind.annotation.*;
import reactor.core.Disposable;
import reactor.core.publisher.Flux;
import reactor.core.publisher.FluxSink;

import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 在线测控
 * <AUTHOR>
 * @description: 在线测控
 * @date 2025-03-10 13:35
 */
@RestController
@RequestMapping("/surveillance")
@RequiredArgsConstructor
@Slf4j
public class SurveillanceController {
    @Autowired
    private ISurveillanceService surveillanceService;

    /**
     * sse方式获取测控数据
     * @param requests
     * @return
     */
    @PostMapping(value = "/sse/getSurveillanceData",produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<ServerSentEvent<Object>> handleBatchRequests(
            @Valid @RequestBody List<ComponentRequest> requests,@RequestParam String taskId
    ) {
        return Flux.create(emitter -> {
            CopyOnWriteArrayList<Disposable> disposables = new CopyOnWriteArrayList<>();
            final AtomicInteger pendingCount = new AtomicInteger(requests.size());
            requests.forEach(request -> {
                Disposable disposable = createStreamForRequest(request,taskId)
                        .subscribe(
                                data -> emitter.next(buildSSEEvent(data)),
                                error -> handleStreamError(emitter, request, error),
                                () -> completeStream(emitter, pendingCount)
                        );
                disposables.add(disposable);
            });

            emitter.onDispose(() -> disposables.forEach(Disposable::dispose));
        });
    }

    private Flux<ComponentResult> createStreamForRequest(ComponentRequest request,String taskId) {
        return Flux.defer(() -> {
            IComponentExecService execService = ComponentFactory.getExecService();
            if(request.isLoop()){
                return createPollingStream(execService,request,taskId);
            }else{
                return execService.functionExec(request,taskId);
            }
        });
    }

    private Flux<ComponentResult> createPollingStream(IComponentExecService execService, ComponentRequest request,String taskId) {
        Duration interval = Duration.ofMillis(
                Math.max(request.getLoopInterval(), 1000)
        );

        return Flux.concat(
                        Flux.just(0L),  // 立即触发第一次执行
                        Flux.interval(interval).skip(0) // 后续按间隔触发，跳过初始的 0
                )
                .onBackpressureDrop()
                .flatMap(tick -> execService.functionExec(request,taskId), 10, 1)
                .takeUntil(ComponentResult::isStopLooping)
                .doOnCancel(() -> log.info("Polling stopped for {}", request.getComponentId()));
    }

    private ServerSentEvent<Object> buildSSEEvent(ComponentResult data) {
        try {

            return ServerSentEvent.builder()
                    .id(data.getComponentId() + "-" + System.currentTimeMillis())
                    .event("data")
                    .retry(Duration.ofSeconds(30))
                    .data(data)
                    .build();
        } catch (Exception e) {
            return ServerSentEvent.builder()
                    .event("error")
                    .data(Map.of(
                            "componentId", data.getComponentId(),
                            "error", "事件构建失败: " + e.getMessage()
                    ))
                    .build();
        }
    }

    private void handleStreamError(FluxSink<ServerSentEvent<Object>> emitter,
                                   ComponentRequest request,
                                   Throwable error) {
        ServerSentEvent<Object> errorEvent = ServerSentEvent.builder()
                .event("error")
                .data(Map.of(
                        "componentId", request.getComponentId(),
                        "error", "数据流异常: " + error.getMessage()
                ))
                .build();
        emitter.next(errorEvent);
        emitter.error(error);
    }

    private void completeStream(FluxSink<ServerSentEvent<Object>> emitter,
                                AtomicInteger pendingCount) {
        if (pendingCount.decrementAndGet() == 0) {
            emitter.complete();
        }
    }

    /**
     * 根据条件查询
     * @param condition
     * @return
     */
    @PostMapping(value = "/findByCondition")
    public UnifyResponse<PageResult<ProductionTaskDTO>> findByCondition(@RequestBody SearchCondition condition) {
        PageResult<ProductionTaskDTO> result = surveillanceService.findByCondition(condition);
        return UnifyResponse.success(result);
    }


    /**
     * 获取任务状态统计信息
     *
     *
     * @return 返回一个UnifyResponse对象，其中包含DataResult类型的对象，该对象包含任务状态统计信息
     */
    @GetMapping(value = "/getTaskStatusStatistics")
    public UnifyResponse<DataResult> getTaskStatusStatistics() {
        // 调用surveillanceService的getTaskStatusStatistics方法获取任务状态统计信息
        DataResult dto = surveillanceService.getTaskStatusStatistics();
        // 返回包含任务状态统计信息的UnifyResponse对象
        return UnifyResponse.success(dto);
    }

    /**
     * 获取在线测控列表数据
     * @param json
     * @return
     */
    @PostMapping(value = "/getSurveillanceList")
    public UnifyResponse<DataResult> getSurveillanceList(@RequestBody String json) {
        DataResult surveillanceList = surveillanceService.getSurveillanceList(json);
        return UnifyResponse.success(surveillanceList);
    }

    /**
     * 获取测控点数据
     * @param json
     * @param pointMethodType
     * @return
     */
    @PostMapping(value = "/fetch/pointData")
    public UnifyResponse<Object> fetchPointData(@RequestBody String json, @RequestParam("pointMethodType") PointMethodType pointMethodType) {
        Object surveillanceList = surveillanceService.fetchPointData(json, pointMethodType);
        return UnifyResponse.success(surveillanceList);
    }

    /**
     * 通过sse方式获取点位历史数据
     * @param jsons
     * @return
     */
    @PostMapping(value = "/batch/detail", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<Object> batchDetail(@RequestBody List<Object> jsons) {
        return Flux.fromIterable(jsons)
                .flatMap(json -> Flux.just(surveillanceService.fetchPointData(json,PointMethodType.batch_point)))
                .defaultIfEmpty(UnifyResponse.success());
    }

    /**
     * 查询扣分明细
     * @param json
     * @return
     */
    @PostMapping(value = "/getScoreDetails")
    public UnifyResponse<DataResult> getScoreDetails(@RequestBody String json,@RequestParam("taskId") String taskId) {
        DataResult dataResult = surveillanceService.getScoreDetails(json,taskId);
        return UnifyResponse.success(dataResult);
    }

    @PostMapping(value = "/findStepResultParams")
    public UnifyResponse<DataResult> findStepResultParams(@RequestBody String json) {
        DataResult dataResult = surveillanceService.findStepResultParams(json);
        return UnifyResponse.success(dataResult);
    }

}
