package com.bzlj.craft.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 工艺参数详情DTO
 * 
 * <AUTHOR>
 * @date 2025-06-18
 */
@Data
public class ProcessParameterDetailDTO {
    
    /**
     * 参数ID
     */
    private String parameterId;
    
    /**
     * 参数定义ID
     */
    private String paramDefId;
    
    /**
     * 参数名称
     */
    private String parameterName;
    
    /**
     * 参数类型
     */
    private String parameterType;
    
    /**
     * 参数单位
     */
    private String unit;
    
    /**
     * 目标值
     */
    private String targetValue;
    
    /**
     * 实际值
     */
    private String actualValue;
    
    /**
     * 数值型实际值
     */
    private BigDecimal numericValue;
    
    /**
     * 文本型实际值
     */
    private String textValue;
    
    /**
     * 日期型实际值
     */
    private LocalDateTime dateValue;
    
    /**
     * 上限值
     */
    private BigDecimal upperLimit;
    
    /**
     * 下限值
     */
    private BigDecimal lowerLimit;
    
    /**
     * 是否合格
     */
    private Boolean isQualified;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;
}
