package com.bzlj.craft.service;

import com.bzlj.craft.dto.TaskQueryResultDTO;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 任务查询服务接口
 * 
 * <AUTHOR>
 * @date 2025-06-18
 */
public interface ITaskQueryService {
    
    /**
     * 根据任务ID查询任务下的执行工步及工艺参数定义及其实际值
     * 使用虚拟线程提高查询速率
     * 
     * @param taskId 任务ID
     * @return 任务查询结果
     */
    CompletableFuture<TaskQueryResultDTO> queryTaskDetailsAsync(String taskId);
    
    /**
     * 批量查询多个任务的详情
     * 使用虚拟线程并行查询
     * 
     * @param taskIds 任务ID列表
     * @return 任务查询结果列表
     */
    CompletableFuture<List<TaskQueryResultDTO>> queryMultipleTaskDetailsAsync(List<String> taskIds);
    
    /**
     * 同步查询任务详情（内部使用虚拟线程优化）
     * 
     * @param taskId 任务ID
     * @return 任务查询结果
     */
    TaskQueryResultDTO queryTaskDetails(String taskId);
}
