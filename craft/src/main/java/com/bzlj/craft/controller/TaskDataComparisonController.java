package com.bzlj.craft.controller;

import com.bzlj.base.response.UnifyResponse;
import com.bzlj.base.result.DataResult;
import com.bzlj.craft.dto.ComponentDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 任务对比分析
 * <AUTHOR>
 * @description:
 * @date 2025-06-10 9:16
 */
@RestController
@RequestMapping("/comparison")
@RequiredArgsConstructor
public class TaskDataComparisonController {

    /**
     * 参数对比
     * @param taskIds
     * @return
     */
//    @PostMapping(value = "/param")
//    public UnifyResponse<DataResult> paramComparison(@RequestParam("taskIds") List<String> taskIds) {
//        List<ComponentDTO> components = componentService.findComponentByIds(ids);
//        return UnifyResponse.success(components);
//    }
}
