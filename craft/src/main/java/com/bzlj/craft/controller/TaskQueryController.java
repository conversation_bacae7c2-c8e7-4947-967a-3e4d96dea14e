package com.bzlj.craft.controller;

import com.bzlj.base.response.UnifyResponse;
import com.bzlj.craft.dto.TaskQueryResultDTO;
import com.bzlj.craft.service.ITaskQueryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 任务查询控制器
 * 提供基于虚拟线程的高性能任务查询接口
 * 
 * <AUTHOR>
 * @date 2025-06-18
 */
@Slf4j
@RestController
@RequestMapping("/task-query")
@RequiredArgsConstructor
public class TaskQueryController {
    
    private final ITaskQueryService taskQueryService;
    
    /**
     * 根据任务ID查询任务详情（同步接口）
     * 内部使用虚拟线程优化查询性能
     * 
     * @param taskId 任务ID
     * @return 任务查询结果
     */
    @GetMapping("/details/{taskId}")
    public UnifyResponse<TaskQueryResultDTO> getTaskDetails(@PathVariable String taskId) {
        log.info("查询任务详情，taskId: {}", taskId);
        
        TaskQueryResultDTO result = taskQueryService.queryTaskDetails(taskId);
        if (result == null) {
            return UnifyResponse.error("任务不存在或查询失败");
        }
        
        return UnifyResponse.success(result);
    }
    
    /**
     * 异步查询任务详情
     * 
     * @param taskId 任务ID
     * @return 异步任务查询结果
     */
    @GetMapping("/details/async/{taskId}")
    public CompletableFuture<UnifyResponse<TaskQueryResultDTO>> getTaskDetailsAsync(@PathVariable String taskId) {
        log.info("异步查询任务详情，taskId: {}", taskId);
        
        return taskQueryService.queryTaskDetailsAsync(taskId)
                .thenApply(result -> {
                    if (result == null) {
                        return UnifyResponse.<TaskQueryResultDTO>error("任务不存在或查询失败");
                    }
                    return UnifyResponse.success(result);
                });
    }
    
    /**
     * 批量查询多个任务的详情
     * 使用虚拟线程并行查询，提高查询效率
     * 
     * @param taskIds 任务ID列表
     * @return 任务查询结果列表
     */
    @PostMapping("/details/batch")
    public UnifyResponse<List<TaskQueryResultDTO>> getBatchTaskDetails(@RequestBody List<String> taskIds) {
        log.info("批量查询任务详情，任务数量: {}", taskIds.size());
        
        if (taskIds.isEmpty()) {
            return UnifyResponse.error("任务ID列表不能为空");
        }
        
        if (taskIds.size() > 100) {
            return UnifyResponse.error("单次查询任务数量不能超过100个");
        }
        
        try {
            List<TaskQueryResultDTO> results = taskQueryService
                    .queryMultipleTaskDetailsAsync(taskIds)
                    .join();
            
            return UnifyResponse.success(results);
        } catch (Exception e) {
            log.error("批量查询任务详情失败", e);
            return UnifyResponse.error("批量查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 异步批量查询多个任务的详情
     * 
     * @param taskIds 任务ID列表
     * @return 异步任务查询结果列表
     */
    @PostMapping("/details/batch/async")
    public CompletableFuture<UnifyResponse<List<TaskQueryResultDTO>>> getBatchTaskDetailsAsync(
            @RequestBody List<String> taskIds) {
        log.info("异步批量查询任务详情，任务数量: {}", taskIds.size());
        
        if (taskIds.isEmpty()) {
            return CompletableFuture.completedFuture(UnifyResponse.error("任务ID列表不能为空"));
        }
        
        if (taskIds.size() > 100) {
            return CompletableFuture.completedFuture(UnifyResponse.error("单次查询任务数量不能超过100个"));
        }
        
        return taskQueryService.queryMultipleTaskDetailsAsync(taskIds)
                .thenApply(UnifyResponse::success)
                .exceptionally(throwable -> {
                    log.error("异步批量查询任务详情失败", throwable);
                    return UnifyResponse.error("异步批量查询失败: " + throwable.getMessage());
                });
    }
    
    /**
     * 获取任务的执行工步信息
     * 
     * @param taskId 任务ID
     * @return 工步信息列表
     */
    @GetMapping("/work-steps/{taskId}")
    public UnifyResponse<List<com.bzlj.craft.dto.WorkStepDetailDTO>> getTaskWorkSteps(@PathVariable String taskId) {
        log.info("查询任务工步信息，taskId: {}", taskId);
        
        TaskQueryResultDTO result = taskQueryService.queryTaskDetails(taskId);
        if (result == null) {
            return UnifyResponse.error("任务不存在或查询失败");
        }
        
        return UnifyResponse.success(result.getWorkSteps());
    }
    
    /**
     * 获取任务的工艺参数信息
     * 
     * @param taskId 任务ID
     * @return 工艺参数信息列表
     */
    @GetMapping("/process-parameters/{taskId}")
    public UnifyResponse<List<com.bzlj.craft.dto.ProcessParameterDetailDTO>> getTaskProcessParameters(
            @PathVariable String taskId) {
        log.info("查询任务工艺参数信息，taskId: {}", taskId);
        
        TaskQueryResultDTO result = taskQueryService.queryTaskDetails(taskId);
        if (result == null) {
            return UnifyResponse.error("任务不存在或查询失败");
        }
        
        return UnifyResponse.success(result.getProcessParameters());
    }
}
