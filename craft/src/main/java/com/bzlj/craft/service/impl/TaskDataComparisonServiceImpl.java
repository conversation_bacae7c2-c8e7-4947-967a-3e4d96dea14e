package com.bzlj.craft.service.impl;

import com.bzlj.craft.command.DataPointSearchCommand;
import com.bzlj.craft.dto.*;
import com.bzlj.craft.entity.ProductionTask;
import com.bzlj.craft.entity.WorkStep;
import com.bzlj.craft.enums.PointMethodType;
import com.bzlj.craft.repository.WorkStepRepository;
import com.bzlj.craft.service.ISurveillanceService;
import com.bzlj.craft.service.ITaskDataComparisonService;
import com.bzlj.craft.util.JsonUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

/**
 * 任务数据对比服务实现类
 * 使用虚拟线程并行查询多个任务的执行工步和工艺参数
 *
 * <AUTHOR>
 * @description: 任务数据对比服务
 * @date 2025-06-10 14:07
 */
@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class TaskDataComparisonServiceImpl implements ITaskDataComparisonService {

    private final ISurveillanceService surveillanceService;
    private final Executor virtualThreadExecutor;
    private final WorkStepRepository workStepRepository;

    public TaskDataComparisonServiceImpl(ISurveillanceService surveillanceService,
                                         WorkStepRepository workStepRepository,
                                         @Qualifier("virtualThreadExecutor") Executor virtualThreadExecutor) {
        this.surveillanceService = surveillanceService;
        this.virtualThreadExecutor = virtualThreadExecutor;
        this.workStepRepository = workStepRepository;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<TaskDetailDTO> paramComparison(List<String> taskIds) {
        log.info("开始使用虚拟线程并行查询任务参数对比数据，任务数量: {}", taskIds.size());
        long startTime = System.currentTimeMillis();

        try {
            // 验证输入参数
            validateTaskIds(taskIds);

            // 使用虚拟线程并行查询每个任务的详细数据
            List<CompletableFuture<TaskDetailDTO>> taskFutures = taskIds.stream()
                    .map(taskId -> CompletableFuture.supplyAsync(() -> {
                        try {
                            return queryTaskDetailWithVirtualThread(taskId);
                        } catch (Exception e) {
                            log.error("查询任务详细数据失败，taskId: {}", taskId, e);
                            return createErrorTaskDetail(taskId, e.getMessage());
                        }
                    }, virtualThreadExecutor))
                    .collect(Collectors.toList());

            // 等待所有任务查询完成
            log.debug("等待所有任务查询完成...");
            List<TaskDetailDTO> results = taskFutures.stream()
                    .map(CompletableFuture::join)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            long endTime = System.currentTimeMillis();
            log.info("任务参数对比查询完成，耗时: {}ms, 成功任务数: {}/{}",
                    endTime - startTime, results.size(), taskIds.size());

            return results;
        } catch (Exception e) {
            log.error("任务参数对比查询失败", e);
            throw new RuntimeException("任务参数对比查询失败: " + e.getMessage(), e);
        }
    }


    private TaskDetailDTO buildTaskDetail(List<ResultParamsDTO> resultParams, String taskId) {
        TaskDetailDTO taskDetailDTO = new TaskDetailDTO();
        taskDetailDTO.setTaskId(taskId);
        Map<String, List<ResultParamsDTO>> map = resultParams.stream().collect(Collectors.groupingBy(ResultParamsDTO::getWorkStepName));
        List<StepDetailDTO> stepDetails = map.keySet().parallelStream().map(workStepName -> {
            StepDetailDTO stepDetailDTO = new StepDetailDTO();
            stepDetailDTO.setStepCode(map.get(workStepName).getFirst().getStepCode());
            stepDetailDTO.setStepName(workStepName);
            stepDetailDTO.setStepOrder(map.get(workStepName).getFirst().getStepOrder());
            List<ResultParamsDTO> resultParamsDTOS = map.get(workStepName);
            List<ParamDetailDTO> paramDetails = resultParamsDTOS.stream().map(resultParamsDTO -> {
                ParamDetailDTO paramDetailDTO = new ParamDetailDTO();
                paramDetailDTO.setParamName(resultParamsDTO.getStepParam());
                paramDetailDTO.setValue(resultParamsDTO.getActualValue());
                paramDetailDTO.setMax(resultParamsDTO.getMax());
                paramDetailDTO.setMin(resultParamsDTO.getMin());
                return paramDetailDTO;
            }).collect(Collectors.toList());
            stepDetailDTO.setParamDetails(paramDetails);
            return stepDetailDTO;
        }).sorted(Comparator.comparing(StepDetailDTO::getStepOrder)).collect(Collectors.toList());
        taskDetailDTO.setStepDetails(stepDetails);
        return taskDetailDTO;
    }

    @Override
    public List<ContinuousStepDTO> continuousParams(String taskId) {
        ProductionTask task = surveillanceService.findByTaskIdWithProcess(taskId);
        return surveillanceService.findContinuousParams(taskId, task.getProcess().getId());
    }

    @Override
    public Map<String, List<DataPointInfoDTO>> getContinuousData(DataPointSearchCommand command, List<String> taskIds, String baseTaskId,String workStepName, PointMethodType pointMethodType) {
        log.info("开始获取连续数据，任务数量: {}, 基准任务: {}", taskIds.size(), baseTaskId);
        long startTime = System.currentTimeMillis();

        try {
            // 使用虚拟线程并行查询各个任务的点位数据
            List<CompletableFuture<TaskPointData>> futures = taskIds.stream()
                    .map(taskId -> CompletableFuture.supplyAsync(() -> {
                        try {
                            return queryTaskPointData(command, taskId,workStepName, pointMethodType);
                        } catch (Exception e) {
                            log.error("查询任务点位数据失败，taskId: {}", taskId, e);
                            return null;
                        }
                    }, virtualThreadExecutor))
                    .collect(Collectors.toList());

            // 等待所有查询完成并收集结果
            List<TaskPointData> taskPointDataList = futures.stream()
                    .map(CompletableFuture::join)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            // 执行点位数据对齐
            Map<String, List<DataPointInfoDTO>> alignedData = alignPointDataWithBase(taskPointDataList, baseTaskId);

            long endTime = System.currentTimeMillis();
            log.info("连续数据获取完成，耗时: {}ms, 成功任务数: {}", endTime - startTime, alignedData.size());

            return alignedData;
        } catch (Exception e) {
            log.error("获取连续数据失败", e);
            throw new RuntimeException("获取连续数据失败: " + e.getMessage(), e);
        }
    }


    /**
     * 查询单个任务的点位数据
     *
     * @param command 查询命令
     * @param taskId 任务ID
     * @param pointMethodType 点位方法类型
     * @return 任务点位数据
     */
    private TaskPointData queryTaskPointData(DataPointSearchCommand command, String taskId,String workStepName, PointMethodType pointMethodType) {
        log.debug("开始查询任务点位数据，taskId: {}", taskId);

        // 创建独立的查询命令副本
        DataPointSearchCommand taskCommand = new DataPointSearchCommand();
        BeanUtils.copyProperties(command, taskCommand);

        // 查询任务信息
        ProductionTask task = surveillanceService.findByTaskIdWithProcess(taskId);
        if (task == null) {
            log.warn("任务不存在，taskId: {}", taskId);
            return null;
        }
        //查询执行工步
        WorkStep workStep = workStepRepository.findFirstByTaskTaskIdAndWorkStepNameAndDeleted(taskId, workStepName, false);
        if(Objects.isNull(workStep)){
            throw new RuntimeException(String.format("未获取到任务%s的执行工步信息",task.getTaskCode()));
        }
        if(workStep.getStartTime() == null || workStep.getEndTime() == null){
            throw new RuntimeException(String.format("未获取到任务%s的执行工步%s的时间信息",task.getTaskCode(),workStep.getWorkStepName()));
        }
        // 设置任务的时间范围
        taskCommand.setStart(workStep.getStartTime().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
        taskCommand.setStop(workStep.getEndTime().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());

        // 查询点位数据
        Map<String, Object> result = (Map<String, Object>) surveillanceService.fetchPointData(Lists.newArrayList(taskCommand), pointMethodType);

        // 构建任务点位数据
        TaskPointData taskPointData = buildTaskPointData(result, taskId, task);

        return taskPointData;
    }

    /**
     * 构建任务点位数据
     *
     * @param result 查询结果
     * @param taskId 任务ID
     * @param task 任务实体
     * @return 任务点位数据
     */
    private TaskPointData buildTaskPointData(Map<String, Object> result, String taskId, ProductionTask task) {
        try {
            TaskPointData taskPointData = new TaskPointData();
            taskPointData.setTaskId(taskId);

            // 解析点位数据
            if (result != null && result.containsKey("data")) {
                Map<String, List<LinkedHashMap<String,Object>>> pointDataMap = (Map<String, List<LinkedHashMap<String,Object>>>)result.get("data");
                List<LinkedHashMap<String, Object>> linkedHashMaps = pointDataMap.values().stream().findFirst().get();
                taskPointData.setPointData(JsonUtils.fromJson(JsonUtils.toJson(linkedHashMaps), new TypeReference<List<DataPointInfoDTO>>() {}));

                // 计算原始时间范围
                calculateTimeRange(taskPointData);
            } else {
                taskPointData.setPointData(Lists.newLinkedList());
            }

            return taskPointData;
        } catch (Exception e) {
            log.error("构建任务点位数据失败，taskId: {}", taskId, e);
            return null;
        }
    }

    /**
     * 计算任务点位数据的时间范围
     *
     * @param taskPointData 任务点位数据
     */
    private void calculateTimeRange(TaskPointData taskPointData) {
        List<DataPointInfoDTO> pointData = taskPointData.getPointData();
//        pointData.sort((o1, o2) -> ((Long)o1.get("ts")).compareTo((Long)o2.get("ts")));
        taskPointData.setMinTimestamp(pointData.getFirst().getTs());
        taskPointData.setMaxTimestamp(pointData.getLast().getTs());
    }

    /**
     * 以基准任务的初始点位信息为原点，对其他任务的点位数据进行对齐
     *
     * @param taskPointDataList 所有任务的点位数据
     * @param baseTaskId 基准任务ID
     * @return 对齐后的点位数据
     */
    private Map<String, List<DataPointInfoDTO>> alignPointDataWithBase(List<TaskPointData> taskPointDataList, String baseTaskId) {
        log.info("开始执行点位数据对齐，基准任务: {}", baseTaskId);

        // 查找基准任务数据
        TaskPointData baseTaskData = taskPointDataList.stream()
                .filter(data -> baseTaskId.equals(data.getTaskId()))
                .findFirst()
                .orElse(null);

        if (baseTaskData == null) {
            log.error("基准任务数据不存在，baseTaskId: {}", baseTaskId);
            return Map.of();
        }

        // 获取基准任务的初始时间戳（原点）
        Long baseOriginTimestamp = baseTaskData.getMinTimestamp();
        if (baseOriginTimestamp == null) {
            log.error("基准任务没有有效的时间戳数据，baseTaskId: {}", baseTaskId);
            throw new RuntimeException("基准任务没有有效的时间戳数据: " + baseTaskId);
        }

        log.info("基准任务原点时间戳: {}, 对应时间: {}", baseOriginTimestamp,
                Instant.ofEpochMilli(baseOriginTimestamp).atZone(ZoneId.systemDefault()).toLocalDateTime());

        // 使用虚拟线程并行处理每个任务的点位数据对齐
        log.debug("开始使用虚拟线程并行对齐任务数据，任务数量: {}", taskPointDataList.size());

        List<CompletableFuture<Map.Entry<String, List<DataPointInfoDTO>>>> alignmentFutures =
                taskPointDataList.stream()
                .map(taskData -> CompletableFuture.supplyAsync(() -> {
                    log.debug("开始对齐任务点位数据，taskId: {}", taskData.getTaskId());
                    long alignStartTime = System.currentTimeMillis();

                    List<DataPointInfoDTO> alignedPoints = alignTaskPointData(taskData, baseOriginTimestamp);

                    long alignEndTime = System.currentTimeMillis();
                    log.debug("任务点位数据对齐完成，taskId: {}, 对齐后点位数量: {}, 耗时: {}ms",
                            taskData.getTaskId(), alignedPoints.size(), alignEndTime - alignStartTime);

                    return Map.entry(taskData.getTaskId(), alignedPoints);
                }, virtualThreadExecutor))
                .collect(Collectors.toList());

        // 等待所有对齐任务完成并收集结果
        log.debug("等待所有对齐任务完成...");
        Map<String, List<DataPointInfoDTO>> alignedDataMap = alignmentFutures.stream()
                .map(CompletableFuture::join)
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (existing, replacement) -> existing,
                        LinkedHashMap::new
                ));

        log.info("点位数据对齐完成，成功对齐任务数: {}", alignedDataMap.size());
        return alignedDataMap;
    }

    /**
     * 对单个任务的点位数据进行对齐
     *
     * @param taskData 任务点位数据
     * @param baseOriginTimestamp 基准原点时间戳
     * @return 对齐后的点位数据
     */
    private List<DataPointInfoDTO> alignTaskPointData(TaskPointData taskData, Long baseOriginTimestamp) {
        List<DataPointInfoDTO> alignedPoints = new ArrayList<>();

        // 获取当前任务的初始时间戳
        Long taskOriginTimestamp = taskData.getMinTimestamp();
        if (taskOriginTimestamp == null) {
            log.warn("任务没有有效的时间戳数据，taskId: {}", taskData.getTaskId());
            return alignedPoints;
        }

        // 计算时间偏移量（当前任务相对于基准任务的时间差）
        long timeOffset = taskOriginTimestamp - baseOriginTimestamp;

        log.debug("任务时间对齐信息 - taskId: {}, 原始起始时间: {}, 时间偏移: {}ms",
                taskData.getTaskId(),
                Instant.ofEpochMilli(taskOriginTimestamp).atZone(ZoneId.systemDefault()).toLocalDateTime(),
                timeOffset);

        for (DataPointInfoDTO originalPoint : taskData.getPointData()) {
            if (originalPoint.getTs() != null) {
                createAlignedPoint(originalPoint, timeOffset);
                alignedPoints.add(originalPoint);
            }
        }

        // 按对齐后的时间戳排序
        alignedPoints.sort(Comparator.comparing(DataPointInfoDTO::getTs));

        return alignedPoints;
    }

    /**
     * 创建对齐后的点位数据
     *
     * @param originalPoint 原始点位数据
     * @param timeOffset 时间偏移量
     * @return 对齐后的点位数据
     */
    private void createAlignedPoint(DataPointInfoDTO originalPoint, long timeOffset) {

        // 计算对齐后的时间戳：原始时间戳 - 时间偏移量
        // 这样所有任务的起始点都会对齐到基准任务的起始时间
        Long alignedTimestamp = originalPoint.getTs() - timeOffset;
        originalPoint.setTs(alignedTimestamp);
    }

    /**
     * 任务点位数据内部类
     */
    @Data
    private static class TaskPointData {
        private String taskId;
//        private List<LinkedHashMap<String,Object>> pointData;
        private List<DataPointInfoDTO> pointData;
        private Long minTimestamp;
        private Long maxTimestamp;

    }

}
