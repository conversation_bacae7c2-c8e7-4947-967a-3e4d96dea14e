package com.bzlj.craft.dto;

import com.bzlj.craft.component.def.FormAttr;
import lombok.Data;

import java.util.List;

/**
 * 组件查询返回对象
 * <AUTHOR>
 * @description:
 * @date 2025-03-11 17:53
 */
@Data
public class ComponentDTO {

    /**
     * 组件id
     */
    private String componentId;

    /**
     * vo模型 全路径
     */
    private String model;

    /**
     * 组件关联查询方法 全路径
     */
    private String methodPath;

    /**
     * 查询参数
     */
    private String params;

    /**
     * 是否轮询
     */
    private Boolean isLoop;

    /**
     * 轮询间隔 毫秒
     */
    private Long loopInterval;

    /**
     * 表单属性集合
     */
    private List<FormAttr> formAttrs;
}
