package com.bzlj.craft.service;

import com.bzlj.craft.dto.TaskQueryResultDTO;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 任务批量查询服务接口
 * 专门用于根据taskIds集合批量查询任务的执行工步和工艺参数
 * 
 * <AUTHOR>
 * @date 2025-06-18
 */
public interface ITaskBatchQueryService {
    
    /**
     * 根据任务ID集合，使用虚拟线程并行查询任务的执行工步和工艺参数
     * 
     * @param taskIds 任务ID集合
     * @return 任务查询结果映射，key为taskId，value为查询结果
     */
    Map<String, TaskQueryResultDTO> batchQueryTaskDetails(List<String> taskIds);
    
    /**
     * 异步批量查询任务详情
     * 
     * @param taskIds 任务ID集合
     * @return 异步任务查询结果映射
     */
    CompletableFuture<Map<String, TaskQueryResultDTO>> batchQueryTaskDetailsAsync(List<String> taskIds);
    
    /**
     * 根据任务ID集合，仅查询执行工步信息
     * 
     * @param taskIds 任务ID集合
     * @return 任务工步映射，key为taskId，value为工步列表
     */
    Map<String, List<com.bzlj.craft.dto.WorkStepDetailDTO>> batchQueryWorkSteps(List<String> taskIds);
    
    /**
     * 根据任务ID集合，仅查询工艺参数信息
     * 
     * @param taskIds 任务ID集合
     * @return 任务参数映射，key为taskId，value为参数列表
     */
    Map<String, List<com.bzlj.craft.dto.ProcessParameterDetailDTO>> batchQueryProcessParameters(List<String> taskIds);
    
    /**
     * 获取查询性能统计信息
     * 
     * @param taskIds 任务ID集合
     * @return 性能统计信息
     */
    Map<String, Object> getQueryPerformanceStats(List<String> taskIds);
}
