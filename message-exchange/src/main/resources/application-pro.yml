eplat:
  ordinary:
    url: ${ORDINARY_URL:http://eplatxt.baocloud.cn/service/S_EPLAT_04}
  telegram:
    url: ${TELEGRAM_URL:http://eplatxt.baocloud.cn/service/S_EPLAT_03}
message:
  ordinary:
    key: ${ORDINARY_KEY:R_GYCK_01} #路由规则，具体现场确认
IXBus:
  serviceId: ${IXBUS_SERVICE:GYCK_TEST_02}

spring:
  cloud:
    stream:
      kafka:
        binder:
          brokers: ${KAFKA:10.81.62.27:9092}
          auto-create-topics: true # 自动创建topics
          required-acks: all
  kafka:
    consumer:
      enable-auto-commit: true #消费者的偏移量是否在后台定期提交。
      auto-offset-reset: latest
        #earliest	从 Topic 的 最早消息（第一条）开始消费。	需要处理所有历史消息（包括旧数据）。
        #latest	从 Topic 的 最新消息（当前写入位置）开始消费，忽略历史消息。	只关心最新数据，不处理积压消息。
      #none	如果无有效 offset，抛出 NoOffsetForPartitionException 异常。	需要严格手动管理 offset 的场景。
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer


dynamic:
  mongo:
    enable-dynamic: true
    primary: primary
    datasource:
      primary:
        name: primary
        host: ${MESSAGE_MONGO_URL:***********}
        port: ${MESSAGE_MONGO_PORT:27017}
        database: ${MESSAGE_MONGO_DATABASE:bwty-craft-dev}
        username: ${MESSAGE_MONGO_USERNAME:root}
        password: ${MESSAGE_MONGO_PWD:BICItech_123}
        authentication-database: admin

queue:
  max-size: 10000