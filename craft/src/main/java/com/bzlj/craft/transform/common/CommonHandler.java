package com.bzlj.craft.transform.common;

import bici.bzlj.dataprocess.core.event.MessageEvent;
import bici.bzlj.dataprocess.core.handler.IMessageHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Map;

/**
 * <AUTHOR>
 * @description:
 * @date 2025-06-06 10:42
 */
@Slf4j
public abstract class CommonHandler<T> implements IMessageHandler<T> {
    public String desc;

    final ZoneOffset offset = ZoneOffset.UTC;

    public abstract void transform(T t);

    /**
     * 消息处理前
     *
     * @param event         消息事件
     * @param handleContext 处理上下文
     */
    @Override
    public void handlePre(MessageEvent<T> event, Map<String, Object> handleContext, Map<String, Object> handlesContext) {
        handleContext.put("startTime", LocalDateTime.now());
        log.info("{}消息{}开始处理", getImplDesc(event.getMessageType()), event.getMessageId());
    }

    @Override
    public void handle(MessageEvent<T> messageEvent, Map<String, Object> map, Map<String, Object> map1) {
        transform(messageEvent.getPayload());
    }

    @Override
    public void handlePost(MessageEvent<T> event, Map<String, Object> currentHandleContext, Map<String, Object> handlesContext){
        LocalDateTime startTime = (LocalDateTime) currentHandleContext.get("startTime");
        long startMillis = startTime.atOffset(offset).toInstant().toEpochMilli();
        long endMillis = LocalDateTime.now().atOffset(offset).toInstant().toEpochMilli();
        log.info("{}消息{}处理完成,耗时:{}ms", getImplDesc(event.getMessageType()),
                event.getMessageId(), endMillis - startMillis);
    }

    public String getImplDesc(String messageType) {
        if (StringUtils.isNotBlank(desc)) {
            return desc;
        }
        this.desc = getDesc(messageType);
        return desc;
    }

    /**
     * 错误处理
     *
     * @param event         错误消息事件
     * @param currentHandleContext 处理上下文
     * @param e             错误信息
     */
    @Override
    public void handleError(MessageEvent<T> event, Map<String, Object> currentHandleContext, Map<String, Object> handlesContext, Exception e) {
        log.error("{}消息{}处理异常:{}", getImplDesc(event.getMessageType()), event.getMessageId(), e.getMessage(), e);
    }
}
