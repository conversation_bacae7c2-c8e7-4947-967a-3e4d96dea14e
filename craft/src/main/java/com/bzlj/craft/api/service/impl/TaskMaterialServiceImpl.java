package com.bzlj.craft.api.service.impl;

import com.bzlj.base.repository.BaseRepository;
import com.bzlj.craft.api.service.ITaskMaterialService;
import com.bzlj.craft.dto.TaskMaterialDTO;
import com.bzlj.craft.entity.TaskMaterial;
import com.bzlj.craft.entity.TaskMaterialId;
import com.bzlj.craft.repository.TaskMaterialRepository;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;

/**
 * <AUTHOR>
 * @description:
 * @date 2025-03-13 13:07
 */
@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class TaskMaterialServiceImpl implements ITaskMaterialService {
    @PersistenceContext
    private EntityManager entityManager;

    @Autowired
    private TaskMaterialRepository repository;

    @Override
    public BaseRepository<TaskMaterial, TaskMaterialId> getRepository() {
        return repository;
    }

    @Override
    public EntityManager getEntityManager() {
        return entityManager;
    }

    @Override
    public Class<TaskMaterialDTO> getDTOClass() {
        return TaskMaterialDTO.class;
    }

    @Override
    public Class<TaskMaterial> getPOClass() {
        return TaskMaterial.class;
    }

    @Override
    public TaskMaterialDTO convertToDto(TaskMaterial entity, Class<TaskMaterialDTO> dtoClass, String... ignoreProperties) {
        TaskMaterialDTO taskMaterialDTO = new TaskMaterialDTO();
        if(Objects.nonNull(entity.getMaterial())){
            taskMaterialDTO.setMaterialId(entity.getMaterial().getMaterialId());
            taskMaterialDTO.setBrand(entity.getMaterial().getBrand());
            taskMaterialDTO.setHeatNumber(entity.getMaterial().getHeatNumber());
            taskMaterialDTO.setIngotNumber(entity.getMaterial().getIngotNumber());
            taskMaterialDTO.setIngotType(entity.getMaterial().getIngotType());
        }
        return taskMaterialDTO;
    }
}
