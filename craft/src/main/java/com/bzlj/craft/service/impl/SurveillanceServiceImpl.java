package com.bzlj.craft.service.impl;


import cn.hutool.core.date.LocalDateTimeUtil;
import com.bzlj.base.repository.BaseRepository;
import com.bzlj.base.result.DataResult;
import com.bzlj.base.result.PageResult;
import com.bzlj.base.search.SearchCondition;
import com.bzlj.base.search.SearchItem;
import com.bzlj.base.search.SearchItems;
import com.bzlj.base.util.DTOConverter;
import com.bzlj.craft.api.service.*;
import com.bzlj.craft.datapoint.api.DataPointService;
import com.bzlj.craft.dto.*;
import com.bzlj.craft.entity.*;
import com.bzlj.craft.enums.ParamType;
import com.bzlj.craft.enums.PointMethodType;
import com.bzlj.craft.enums.TaskStatus;
import com.bzlj.craft.enums.TeamRole;
import com.bzlj.craft.mongo.entity.AbnormalRecord;
import com.bzlj.craft.mongo.entity.AlarmContent;
import com.bzlj.craft.mongo.repository.AbnormalRecordRepository;
import com.bzlj.craft.repository.*;
import com.bzlj.craft.service.ISurveillanceService;
import com.bzlj.craft.util.JsonUtils;
import com.bzlj.craft.vo.surveillance.*;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.querydsl.core.types.Expression;
import com.querydsl.core.types.Projections;
import com.querydsl.core.types.dsl.CaseBuilder;
import com.querydsl.jpa.impl.JPAQueryFactory;
import jakarta.annotation.Resource;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.bzlj.craft.entity.QProductionTask.productionTask;
import static com.bzlj.craft.enums.AlertType.manual;
import static com.bzlj.craft.enums.AlertType.system;
import static com.bzlj.craft.exception.BiciErrorData.*;

/**
 * <AUTHOR>
 * @description: 在线测控
 * @date 2025-03-10 13:34
 */
@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class SurveillanceServiceImpl implements ISurveillanceService {

    @PersistenceContext
    private EntityManager entityManager;

    @Autowired
    private ProductionTaskRepository repository;

    @Autowired
    private ProductionTaskExtendRepository productionTaskExtendRepository;

    @Autowired
    private ParameterDefinitionRepository parameterDefinitionRepository;

    @Autowired
    private IProcessParameterService processParameterService;
    @Autowired
    private ProductionTaskRepository productionTaskRepository;
    @Autowired
    private TaskEquipmentRepository taskEquipmentRepository;

    @Override
    public BaseRepository<ProductionTask, String> getRepository() {
        return repository;
    }

    @Override
    public EntityManager getEntityManager() {
        return entityManager;
    }

    @Override
    public Class<ProductionTaskDTO> getDTOClass() {
        return ProductionTaskDTO.class;
    }

    @Override
    public Class<ProductionTask> getPOClass() {
        return ProductionTask.class;
    }

    @Autowired
    JPAQueryFactory queryFactory;

    private static final QProductionTask qTask = productionTask;

    private static final String IN_PROGRESS_CODE = "in_progress";
    private static final String NOT_STARTED_CODE = "not_start";
    private static final String COMPLETED_CODE = "completed";

    @Autowired
    private ProcessStepRepository processStepRepository;

    @Autowired
    private WorkStepRepository workStepRepository;

    @Autowired
    private IOperationLogService operationLogService;

    @Autowired
    private EmployeeRepository employeeRepository;

    @Autowired
    private IQualityInspectionService qualityInspectionService;

    @Autowired
    private ITaskMaterialService taskMaterialService;

    @Autowired
    private IProduceReportLoadMaterialService produceReportLoadMaterialService;


    @Autowired
    private ShiftHandoverRepository shiftHandoverRepository;

    @Autowired
    private EmployeeTeamRepository employeeTeamRepository;

    @Autowired
    private IDataPointInfoService dataPointInfoService;

    @Autowired
    private AbnormalRecordRepository abnormalRecordRepository;

    @Resource
    DataPointService dataPointService;

    @Autowired
    private IProcessScoreDetailsService processScoreDetailsService;

    @Override
    public ProductionTaskDTO convertToDto(ProductionTask entity, Class<ProductionTaskDTO> dtoClass, String... ignoreProperties) {
        ProductionTaskDTO dto = createInstance(dtoClass);
        Set<TaskMaterial> taskMaterials = entity.getTaskMaterials();
        List<String> brands = new ArrayList<>();
        if (!CollectionUtils.isEmpty(taskMaterials)) {
            Map<Boolean, List<TaskMaterial>> relationMap = taskMaterials.stream().collect(Collectors.groupingBy(TaskMaterial::getRelationType));
            relationMap.forEach((relation, value) -> {
                List<Material> materials = value.stream().filter(taskMaterial -> Objects.nonNull(taskMaterial.getMaterial())).map(TaskMaterial::getMaterial).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(materials)) {
                    String materialCodes = materials.stream().map(Material::getMaterialCode).collect(Collectors.joining(","));
                    if (BooleanUtils.isTrue(relation)) {
                        dto.setOutputMaterialCode(materialCodes);
                    } else {
                        dto.setInputMaterialCode(materialCodes);
                    }
                    List<String> materialBrands = materials.stream().filter(material -> StringUtils.isNotEmpty(material.getBrand())).map(Material::getBrand).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(materialBrands)) {
                        brands.addAll(materialBrands);
                    }
                }
            });
        }
        if (!CollectionUtils.isEmpty(brands)) {
            dto.setBrand(String.join(",", brands.stream().distinct().toList()));
        }
        if (Objects.nonNull(entity.getProcess())) {
            dto.setProcessId(entity.getProcess().getId());
            if (Objects.nonNull(entity.getProcess().getProcessType())) {
                dto.setProcessType(entity.getProcess().getProcessType().getItemName());
            }
            if (Objects.nonNull(entity.getProcess().getCraft())) {
                dto.setCraftCode(entity.getProcess().getCraft().getCraftCode());
            }
        }
        if (!CollectionUtils.isEmpty(entity.getTaskEquipments())) {
            List<Equipment> equipments = entity.getTaskEquipments().stream().filter(taskEquipment -> Objects.nonNull(taskEquipment.getEquip())).map(TaskEquipment::getEquip).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(equipments)) {
                String equipmentCodes = equipments.stream().map(Equipment::getCode).collect(Collectors.joining(","));
                String equipmentNames = equipments.stream().map(Equipment::getName).collect(Collectors.joining(","));
                dto.setEquipmentCode(equipmentCodes);
                dto.setEquipmentName(equipmentNames);
            }
        }
        dto.setPlanEndTime(entity.getPlanEndTime());
        dto.setPlanStartTime(entity.getPlanStartTime());

        dto.setTaskId(entity.getTaskId());
        dto.setTaskCode(entity.getTaskCode());
        dto.setStartTime(entity.getStartTime());
        dto.setEndTime(entity.getEndTime());
        dto.setPlanWeight(entity.getPlanWeight());
        dto.setPlanQuantity(entity.getPlanQuantity());
        dto.setScore(entity.getScore());
        if (Objects.nonNull(entity.getStatusCode())) {
            dto.setStatusCode(entity.getStatusCode().getItemCode());
        }
        return dto;
    }

    @Override
    public DataResult getSurveillanceList(String json) {
        SearchCondition searchCondition = JsonUtils.fromJson(json, SearchCondition.class);
        PageResult<ProductionTaskDTO> result = findByCondition(searchCondition);
        DataResult dataResult = new DataResult();
        dataResult.setTotal(result.getTotal());
        dataResult.setPageSize(result.getPageSize());
        dataResult.setPageCurrent(result.getPageCurrent());
        if (CollectionUtils.isEmpty(result.getContent())) {
            dataResult.setList(Lists.newArrayList());
            return dataResult;
        }
        List<List<Object>> value = DTOConverter.convert(result.getContent(), ProductionTaskVO.class);
        dataResult.setList(value);
        return dataResult;
    }


    @Override
    public DataResult getTaskStatusStatistics() {
        TaskStatisticsDTO taskStatisticsDTO = queryFactory.select(
                        Projections.constructor(
                                TaskStatisticsDTO.class,
                                qTask.count(),
                                createStatusCountExpression(IN_PROGRESS_CODE),
                                createStatusCountExpression(NOT_STARTED_CODE),
                                createStatusCountExpression(COMPLETED_CODE)
                        ))
                .from(qTask)
                .fetchOne();
        List<List<Object>> value = DTOConverter.convert(Lists.newArrayList(taskStatisticsDTO), TaskStatisticsVO.class);
        DataResult dataResult = new DataResult();
        dataResult.setList(value);
        return dataResult;
    }

    private Expression<Long> createStatusCountExpression(String statusCode) {
        return new CaseBuilder()
                .when(qTask.statusCode.itemCode.eq(statusCode))
                .then(1L)
                .otherwise(0L)
                .sum()
                .coalesce(0L) // 处理null值，转换为0
                .as(statusCode + "_count");
    }

    @Override
    @Transactional
    public DataResult findSurveillanceStep(String json) {
        SearchCondition searchCondition = JsonUtils.fromJson(json, SearchCondition.class);
        List<ProductionTaskDTO> tasks = findAllWithCondition(searchCondition);
        List<WorkStepDTO> workSteps = new ArrayList<>();
        if (!CollectionUtils.isEmpty(tasks)) {
            workSteps = findWorkStepByTask(tasks.getFirst());
        }
        DataResult dataResult = new DataResult();
        if (CollectionUtils.isEmpty(workSteps)) {
            dataResult.setList(Lists.newArrayList());
            return dataResult;
        }
        dataResult.setList(DTOConverter.convert(workSteps, WorkStepVO.class));
        return dataResult;
    }

    @Override
    public List<WorkStepDTO> findWorkStepByTask(ProductionTaskDTO task) {
        if (Objects.nonNull(task.getProcessId())) {
            List<ProcessStep> processSteps = processStepRepository.findByProcessIdOrderByStepOrderAsc(task.getProcessId());
            //只查询原始工序上的执行工步单元
            List<String> stepIds = processSteps.stream().map(ProcessStep::getId).collect(Collectors.toList());
            List<WorkStep> workSteps = workStepRepository.findByStepIdInAndTaskTaskIdAndDeleted(stepIds, task.getTaskId(),false);
            workSteps.sort(Comparator.comparing(WorkStep::getWorkStepOrder));
            List<WorkStepDTO> steps = workSteps.stream().map(workStep -> {
                WorkStepDTO dto = new WorkStepDTO();
                dto.setId(workStep.getWorkStepId());
                dto.setStepId(workStep.getStep().getId());
                dto.setWorkStepName(workStep.getWorkStepName());
                dto.setStepCode(workStep.getStep().getStepCode());
                dto.setWorkStepOrder(workStep.getWorkStepOrder());
                dto.setStartTime(workStep.getStartTime());
                dto.setEndTime(workStep.getEndTime());
                if (Objects.nonNull(workStep.getStatusCode())) {
                    dto.setStatusCode(workStep.getStatusCode().getItemCode());
                }
                dto.setTaskId(task.getTaskId());
                return dto;
            }).collect(Collectors.toList());

            return steps;
        }
        return Lists.newArrayList();
    }


    @Override
    public DataResult findStepResultParams(String json) {
        DataResult dataResult = new DataResult();
        dataResult.setList(Lists.newArrayList());
        SearchCondition searchCondition = JsonUtils.fromJson(json, SearchCondition.class);
        List<ProductionTaskDTO> tasks = findAllWithCondition(searchCondition);
        List<WorkStepDTO> workSteps = new ArrayList<>();
        if (!CollectionUtils.isEmpty(tasks)) {
            workSteps = findWorkStepByTask(tasks.getFirst());
        }
        List<String> workStepIds = workSteps.stream().map(WorkStepDTO::getId).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(workStepIds)) {
            ImmutableMap<String, WorkStepDTO> workStepMap = Maps.uniqueIndex(workSteps, WorkStepDTO::getId);
            List<ParameterDefinition> parameterDefinitions = parameterDefinitionRepository.findByWorkStepWorkStepIdInAndParamType_ItemCodeIn(workStepIds, Lists.newArrayList(ParamType.NUMBER.getCode(), ParamType.TEXT.getCode(), ParamType.DATE.getCode()));
            if (!CollectionUtils.isEmpty(parameterDefinitions)) {
                List<String> paramDefIds = parameterDefinitions.stream().map(ParameterDefinition::getParamDefId).collect(Collectors.toList());
                List<ProcessParameter> processParameters = processParameterService.findByParamDefAndTask(paramDefIds, workSteps.stream().findFirst().get().getTaskId());
                Map<String, List<ProcessParameter>> parameterMap = processParameters.stream().collect(Collectors.groupingBy(processParameter -> processParameter.getParamDef().getParamDefId()));
                List<ResultParamsDTO> resultParams = parameterDefinitions.stream().map(parameterDefinition -> {
                    ResultParamsDTO resultParamsDTO = new ResultParamsDTO();
                    WorkStepDTO workStep = workStepMap.get(parameterDefinition.getWorkStep().getWorkStepId());
                    resultParamsDTO.setId(workStep.getId());
                    resultParamsDTO.setStepCode(workStep.getStepCode());
                    resultParamsDTO.setWorkStepName(workStep.getWorkStepName());
                    resultParamsDTO.setStepOrder(workStep.getWorkStepOrder());
                    resultParamsDTO.setStepParam(parameterDefinition.getParamName());
                    resultParamsDTO.setStandard(String.format("%s~%s %s", Objects.isNull(parameterDefinition.getMinValue()) ? "" : parameterDefinition.getMinValue().stripTrailingZeros().toPlainString(),
                            Objects.isNull(parameterDefinition.getMaxValue()) ? "" : parameterDefinition.getMaxValue().stripTrailingZeros().toPlainString(), StringUtils.isNotBlank(parameterDefinition.getUnit()) ? parameterDefinition.getUnit() : ""));
                    List<ProcessParameter> parameters = parameterMap.get(parameterDefinition.getParamDefId());
                    resultParamsDTO.setActualValue("-");
                    if (!CollectionUtils.isEmpty(parameters)) {
                        ProcessParameter parameter = parameters.getFirst();
                        switch (ParamType.valueOf(parameterDefinition.getParamType().getItemCode())) {
                            case NUMBER:
                                ProcessNumParameter processNumParameter = parameter.getProcessNumParameter();
                                if(Objects.nonNull(processNumParameter) && Objects.nonNull(processNumParameter.getActualValue())){
                                    resultParamsDTO.setActualValue(processNumParameter.getActualValue().stripTrailingZeros().toPlainString());
                                }
                                break;
                            case DATE:
                                ProcessDateParameter processDateParameter = parameter.getProcessDateParameter();
                                if(Objects.nonNull(processDateParameter) && Objects.nonNull(processDateParameter.getActualValue())){
                                    resultParamsDTO.setActualValue(LocalDateTimeUtil.format(processDateParameter.getActualValue(), "yyyy-MM-dd HH:mm:ss"));
                                }
                                break;
                            case TEXT:
                                ProcessTextParameter processTextParameter = parameter.getProcessTextParameter();
                                if(Objects.nonNull(processTextParameter) && !StringUtils.isEmpty(processTextParameter.getActualValue())){
                                    resultParamsDTO.setActualValue(processTextParameter.getActualValue());
                                }
                                break;
                            default:
                                break;
                        }
                    }
                    return resultParamsDTO;
                }).collect(Collectors.toList());
                dataResult.setList(DTOConverter.convert(resultParams, ResultParamsVO.class));
            }
        }

        return dataResult;
    }

    @Override
    public DataResult findOperation(String json) {
        DataResult dataResult = new DataResult();
        dataResult.setList(Lists.newArrayList());
        SearchCondition searchCondition = JsonUtils.fromJson(json, SearchCondition.class);
        searchCondition.setOpenProps(Lists.newArrayList("task", "workstep"));
        List<OperationLogDTO> operationLogDTOS = operationLogService.findAllWithCondition(searchCondition);
        if (!CollectionUtils.isEmpty(operationLogDTOS)) {
            List<String> operatorIds = operationLogDTOS.stream().filter(operationLogDTO -> Objects.nonNull(operationLogDTO.getOperatorId()))
                    .map(OperationLogDTO::getOperatorId).collect(Collectors.toList());
            Map<String, Employee> operatorMap = new HashMap<>();
            if (!CollectionUtils.isEmpty(operatorIds)) {
                List<Employee> employees = employeeRepository.findAllById(operatorIds);
                operatorMap.putAll(Maps.uniqueIndex(employees, Employee::getId));
            }
            operationLogDTOS.forEach(operationLogDTO -> {
                Employee employee = operatorMap.get(operationLogDTO.getOperatorId());
                if (Objects.nonNull(employee)) {
                    operationLogDTO.setOperator(employee.getFullName());
                }
            });
            dataResult.setList(DTOConverter.convert(operationLogDTOS, OperationLogVO.class));
        }
        return dataResult;
    }

    @Override
    public DataResult findInspectionData(String json) {
        DataResult dataResult = new DataResult();
        dataResult.setList(Lists.newArrayList());
        SearchCondition searchCondition = JsonUtils.fromJson(json, SearchCondition.class);
        Map<String, Object> inspectionData = qualityInspectionService.findInspectionData(searchCondition);
        if (CollectionUtils.isEmpty(inspectionData)) return dataResult;
        Object tabs = inspectionData.get("tabNames");
        if (Objects.nonNull(tabs)) {
            dataResult.setTabNames((List) tabs);
        }
        Object data = inspectionData.get("data");
        if (Objects.nonNull(data)) {
            dataResult.setList((List) data);
        }
        return dataResult;
    }

    @Override
    public DataResult findLoadMaterialRecorde(String json) {
        DataResult dataResult = new DataResult();
        dataResult.setList(Lists.newArrayList());
        SearchCondition searchCondition = JsonUtils.fromJson(json, SearchCondition.class);
        searchCondition.setOpenProps(Lists.newArrayList("material", "createUser"));
        List<ProduceReportLoadMaterialDTO> produceReportLoadMaterials = produceReportLoadMaterialService.findAllWithCondition(searchCondition);
        if (!CollectionUtils.isEmpty(produceReportLoadMaterials)) {
            dataResult.setList(DTOConverter.convert(produceReportLoadMaterials, ProduceReportLoadMaterialVO.class));
        }
        return dataResult;
    }

    @Override
    public DataResult findProductionMaterials(String json) {
        DataResult dataResult = new DataResult();
        dataResult.setList(Lists.newArrayList());
        SearchCondition searchCondition = JsonUtils.fromJson(json, SearchCondition.class);
        searchCondition.setOpenProps(Lists.newArrayList("material"));
        List<TaskMaterialDTO> taskMaterialDTOS = taskMaterialService.findAllWithCondition(searchCondition);
        if (!CollectionUtils.isEmpty(taskMaterialDTOS)) {
            dataResult.setList(DTOConverter.convert(taskMaterialDTOS, TaskMaterialVO.class));
        }
        return dataResult;
    }


    @Override
    public DataResult findTeamInfo(String json) {
        if (StringUtils.isEmpty(json) || Objects.isNull(JsonUtils.toJsonNode(json).get("taskId"))) {
            throw TASK_ID_IS_NULL.buildException();
        }
        String taskId = JsonUtils.toJsonNode(json).get("taskId").asText();
        DataResult dataResult = new DataResult();
        dataResult.setList(Lists.newArrayList());
        List<ShiftHandover> shiftHandovers = shiftHandoverRepository.findByTaskTaskIdOrderByHandoverTimeAsc(taskId);
        if (CollectionUtils.isEmpty(shiftHandovers)) return dataResult;
        List<ShiftHandover> inComingHandovers = shiftHandovers.stream().filter(shiftHandover -> Objects.nonNull(shiftHandover.getIncomingTeam()))
                .sorted(Comparator.comparing(ShiftHandover::getHandoverTime))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(inComingHandovers)) return dataResult;
        List<String> teamIds = inComingHandovers.stream().map(shiftHandover -> shiftHandover.getIncomingTeam().getTeamId()).collect(Collectors.toList());
        List<EmployeeTeam> employeeTeams = employeeTeamRepository.findByTeamTeamIdIn(teamIds);
        Map<String, List<EmployeeTeam>> employeeTeamMap;
        if (!CollectionUtils.isEmpty(employeeTeams)) {
            employeeTeamMap = employeeTeams.stream().collect(Collectors.groupingBy(employeeTeam -> employeeTeam.getTeam().getTeamId()));
        } else {
            employeeTeamMap = new HashMap<>();
        }
        AtomicInteger num = new AtomicInteger(1);
        List<TeamInfoDTO> teamInfos = inComingHandovers.stream().map(shiftHandover -> {
            Team incomingTeam = shiftHandover.getIncomingTeam();
            TeamInfoDTO teamInfoDTO = new TeamInfoDTO();
            teamInfoDTO.setNum(num.get());
            teamInfoDTO.setTeamId(incomingTeam.getTeamId());
            teamInfoDTO.setTeamName(incomingTeam.getTeamName());
            teamInfoDTO.setHandoverTime(shiftHandover.getHandoverTime());
            num.getAndIncrement();
            List<EmployeeTeam> employeeTeamList = employeeTeamMap.get(incomingTeam.getTeamId());
            if (!CollectionUtils.isEmpty(employeeTeamList)) {
                Map<String, List<EmployeeTeam>> roleMap = employeeTeamList.stream().collect(Collectors.groupingBy(EmployeeTeam::getRole));
                teamInfoDTO.setMembers(CollectionUtils.isEmpty(roleMap.get(TeamRole.member.getCode())) ?
                        null : roleMap.get(TeamRole.member.getCode()).stream()
                        .map(employeeTeam -> employeeTeam.getEmployee().getFullName())
                        .distinct()
                        .collect(Collectors.joining(",")));
                teamInfoDTO.setGroupLeader(CollectionUtils.isEmpty(roleMap.get(TeamRole.leader.getCode())) ?
                        null : roleMap.get(TeamRole.leader.getCode()).stream()
                        .map(employeeTeam -> employeeTeam.getEmployee().getFullName())
                        .distinct()
                        .collect(Collectors.joining(",")));
            }
            return teamInfoDTO;
        }).toList();
        if (!CollectionUtils.isEmpty(teamInfos)) {
            dataResult.setList(DTOConverter.convert(teamInfos, TeamInfoVO.class));
        }
        return dataResult;
    }

    @Override
    public DataResult findContinuousParams(String json) {
        DataResult dataResult = new DataResult();
        dataResult.setList(Lists.newArrayList());
        SearchCondition searchCondition = JsonUtils.fromJson(json, SearchCondition.class);
        List<ProductionTaskDTO> tasks = findAllWithCondition(searchCondition);
        List<WorkStepDTO> workSteps = new ArrayList<>();
        ProductionTaskDTO task;
        if (!CollectionUtils.isEmpty(tasks)) {
            task = tasks.getFirst();
            workSteps = findWorkStepByTask(task);
        } else {
            task = null;
        }
        List<String> workStepIds = workSteps.stream().map(WorkStepDTO::getId).collect(Collectors.toList());
        workSteps.stream().collect(Collectors.groupingBy(WorkStepDTO::getId));
        if (!CollectionUtils.isEmpty(workStepIds)) {
            //查询点位信息
            List<DataPointInfo> dataPointInfos = dataPointInfoService.findByWorkStepIds(workStepIds);

            Map<String, List<DataPointInfo>> dataPointMap = dataPointInfos.stream().collect(Collectors.groupingBy(dataPointInfo -> String.format("%s_%s", dataPointInfo.getWorkStep().getWorkStepId(), dataPointInfo.getParamDef().getParamDefId())));
            ImmutableMap<String, WorkStepDTO> workStepMap = Maps.uniqueIndex(workSteps, WorkStepDTO::getId);
            List<ParameterDefinition> parameterDefinitions = parameterDefinitionRepository.findByWorkStepWorkStepIdInAndParamType_ItemCodeIn(workStepIds, Lists.newArrayList(ParamType.CURVE.getCode()));
            if (!CollectionUtils.isEmpty(parameterDefinitions)) {
                List<ContinuousStepDTO> stepDTOS = new ArrayList<>();
                Map<String, List<ParameterDefinition>> parameterMap = parameterDefinitions.stream().collect(Collectors.groupingBy(parameterDefinition -> parameterDefinition.getWorkStep().getWorkStepId()));
                parameterMap.forEach((stepId, parameters) -> {
                    ContinuousStepDTO stepDTO = new ContinuousStepDTO();
                    WorkStepDTO workStep = workStepMap.get(stepId);
                    stepDTO.setWorkStepId(workStep.getId());
                    stepDTO.setStepCode(workStep.getStepCode());
                    stepDTO.setWorkStepName(workStep.getWorkStepName());
                    stepDTO.setStepOrder(workStep.getWorkStepOrder());
                    stepDTO.setStartTime(workStep.getStartTime());
                    stepDTO.setEndTime(workStep.getEndTime());
                    List<ContinuousParamsDTO> paramsDTOS = parameters.stream().map(parameterDefinition -> {
                        List<DataPointInfo> dataPoints = dataPointMap.get(String.format("%s_%s", stepId, parameterDefinition.getParamDefId()));

                        ContinuousParamsDTO continuousParams = new ContinuousParamsDTO();
                        continuousParams.setStepParam(parameterDefinition.getParamName());
                        continuousParams.setId(parameterDefinition.getParamDefId());
                        if (!CollectionUtils.isEmpty(dataPoints)) {
                            continuousParams.setDataPointCode(dataPoints.stream().findFirst().get().getDataPoint());
                        }
                        return continuousParams;
                    }).collect(Collectors.toList());
                    stepDTO.setContinuousParams(paramsDTOS);
                    stepDTOS.add(stepDTO);
                });
                if (!CollectionUtils.isEmpty(stepDTOS)) {
                    dataResult.setList(DTOConverter.convert(stepDTOS, ContinuousStepVO.class));
                }
            }
        }
        return dataResult;
    }

    @Override
    public DataResult findAbnormalRecords(String json) {
        if (StringUtils.isEmpty(json) || Objects.isNull(JsonUtils.toJsonNode(json).get("taskId"))) {
            throw TASK_ID_IS_NULL.buildException();
        }
        DataResult dataResult = new DataResult();
        dataResult.setList(Lists.newArrayList());
        String taskId = JsonUtils.toJsonNode(json).get("taskId").asText();
        List<AbnormalRecord> records = abnormalRecordRepository.findByTaskIdOrderByAlertTimeDesc(taskId);
        if (CollectionUtils.isEmpty(records)) return dataResult;
        List<AbnormalRecordDTO> abnormalRecordDTOS = records.stream().map(record -> {
            AbnormalRecordDTO abnormalRecordDTO = new AbnormalRecordDTO();
            abnormalRecordDTO.setAlertTime(record.getAlertTime());
            abnormalRecordDTO.setAlertType(BooleanUtils.isTrue(record.getAlertType()) ? manual.getMsg() : system.getMsg());
            AlarmContent alarmContent = record.getAlarmContent();
            abnormalRecordDTO.setAlarmContent(String.format("工步-%s，“%s”%s;优化建议：%s", alarmContent.getWorkStepName(), alarmContent.getParameterName(), alarmContent.getAlarmContent(), alarmContent.getSuggestion()));
            return abnormalRecordDTO;
        }).toList();
        dataResult.setList(DTOConverter.convert(abnormalRecordDTOS, AbnormalRecordVO.class));
        return dataResult;
    }

    @Override
    public Object fetchPointData(Object json, PointMethodType pointMethodType) {
        Object result = null;
        log.info("查询点位数据，请求参数：{}", json);
        log.info("查询点位数据，调用接口：{}", pointMethodType.getMsg());
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        result = switch (pointMethodType) {
            case batch_point, single_point -> dataPointService.batchFindHistory(json);
            case batch_concurrent_point -> dataPointService.batchList(json);
            case first_point_in_range_date -> dataPointService.firstDetail(json);
            case latest_point_in_range_date -> dataPointService.lastDetail(json);
            case max_point_in_range_date -> dataPointService.maxDetail(json);
            case min_point_in_range_date -> dataPointService.minDetail(json);
            case mean_point_in_range_date -> dataPointService.meanDetail(json);
            case sum_point_in_range_date -> dataPointService.sumDetail(json);
            case end_subtract_start_point -> dataPointService.endSubtractStartDetail(json);
            case spread_point_in_range_date -> dataPointService.spreadDetail(json);
            case upper_lower_point_in_range_date -> dataPointService.upperLowerDetail(json);
            case status_statistics_boolean -> dataPointService.statusStatisticsBooleanDetail(json);
            case end_subtract_start_point_2 -> dataPointService.endSubtractStart2Detail(json);
            case calculator_point_in_range_date -> dataPointService.calculator(json);
        };
        stopWatch.stop();
        log.info("查询点位数据，调用接口：{}，耗时：{}毫秒", pointMethodType.getMsg(), stopWatch.getTotalTimeMillis());
        return result;
    }

    @Override
    public String findTaskStatus(String taskId) {
        SearchCondition condition = new SearchCondition();
        SearchItems searchItems = SearchItems.builder()
                .item(new SearchItem("taskId", taskId, null, SearchItem.Operator.EQ))
                .build();
        condition.setSearchItems(searchItems);
        List<String> openProps = Lists.newArrayList("statusCode");
        condition.setOpenProps(openProps);
        List<ProductionTask> tasks = this.findEntityWithCondition(condition);
        if (CollectionUtils.isEmpty(tasks)) return null;
        SysDictItem statusCode = tasks.getFirst().getStatusCode();
        return statusCode.getItemCode();
    }

    /*public void test() {
        // 定义窗口函数：ROW_NUMBER() OVER (PARTITION BY task_name, process_id ORDER BY task_id)
        Expression<Long> rowNumber = SQLExpressions.rowNumber()
                .over()
                .partitionBy(qTask.taskCode)
                .orderBy(qTask.taskId.asc())
                .as("rn");
        // 构建子查询：SELECT *, ROW_NUMBER()... FROM production_task WHERE step_id = 1
        JPAQuery<ProductionTask> subQuery = new JPAQuery<>(entityManager);
        subQuery.select(qTask.taskId, qTask.endTime,
                        qTask.planEndTime, qTask.planQuantity,
                        qTask.planStartTime, qTask.planWeight,
                        Expressions.template(String.class, "productionTask.process_id").as("process_id"),
                        Expressions.template(String.class, "productionTask.step_id").as("step_id"),
                        qTask.startTime, qTask.taskCode, qTask.workshopId,
                        Expressions.template(String.class, "productionTask.status_code").as("status_code"),
                        rowNumber)
                .from(qTask);
        QProductionTask task = new QProductionTask("task");
        // 外层查询：SELECT * FROM (subQuery) WHERE rn = 1
        Path<Long> rnAlias = Expressions.numberPath(Long.class, "rn");
        JPASQLQuery<ProductionTask> query = new JPASQLQuery<>(entityManager, new OracleTemplates());
        // 将子查询转换为 SubQueryExpression 并赋予别名 "sub"
        List<ProductionTask> result = query.select(Projections.bean(ProductionTask.class, task))
//                .from(Expressions.as(subQuery, "task"))
                .from(subQuery, Expressions.stringPath("task"))
                .where(Expressions.predicate(Ops.EQ, rnAlias, Expressions.constant(1L)))
                .fetch();

    }*/

    @Override
    public DataResult getScoreDetails(String json,String taskId) {
        if(StringUtils.isEmpty(taskId)) throw TASK_ID_IS_NULL.buildException();
        DataResult dataResult = new DataResult();
        dataResult.setList(Lists.newArrayList());
        SearchCondition searchCondition = JsonUtils.fromJson(json, SearchCondition.class);
        SearchItems searchItems = searchCondition.getSearchItems();
        if(Objects.isNull(searchItems)){
            searchCondition.setSearchItems(SearchItems.builder()
                    .item(new SearchItem("task.id", taskId, null, SearchItem.Operator.EQ))
                    .build());
        }else{
            searchItems.addItem(new SearchItem("task.id", taskId, null, SearchItem.Operator.EQ));
        }
        PageResult<ProcessScoreDetailsDTO> result = processScoreDetailsService.findByCondition(searchCondition);
        dataResult.setTotal(result.getTotal());
        dataResult.setPageSize(result.getPageSize());
        dataResult.setPageCurrent(result.getPageCurrent());
        if (CollectionUtils.isEmpty(result.getContent())) {
            dataResult.setList(Lists.newArrayList());
            return dataResult;
        }
        List<List<Object>> value = DTOConverter.convert(result.getContent(), ProcessScoreDetailsVO.class);
        dataResult.setList(value);
        return dataResult;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteTask(String taskCode) {
        ProductionTask productionTask = repository.findFirstByTaskCodeAndDeleted(taskCode, false);
        if(Objects.isNull(productionTask)){
            log.error("{}:状态更改的任务不存在", taskCode);
            throw new RuntimeException(String.format("%s:任务撤销的任务不存在", taskCode));
        }

        deleteTaskExtend(productionTask.getTaskId());
        deleteTaskEquipment(productionTask.getTaskId());
        deleteWorkStep(productionTask.getTaskId());

        productionTask.setDeleted(true);
        this.update(productionTask);
        log.info("{}:删除成功",  productionTask.getTaskCode());
    }

    private void deleteTaskExtend(String taskId){
        List<ProductionTaskExtend> extendList = productionTaskExtendRepository.findByTaskTaskIdAndDeleted(taskId, false);
        if(!CollectionUtils.isEmpty(extendList)){
            extendList.forEach(extend->extend.setDeleted(true));
            productionTaskExtendRepository.saveAll(extendList);
        }
    }

    private void deleteTaskEquipment(String taskId){
        //删除设备关联关系
        List<TaskEquipment> taskEquipments = taskEquipmentRepository.findByTaskTaskIdAndDeleted(taskId, false);
        if(!CollectionUtils.isEmpty(taskEquipments)){
            taskEquipments.forEach(taskEquipment->taskEquipment.setDeleted(true));
            taskEquipmentRepository.saveAll(taskEquipments);
        }
    }

    private void deleteWorkStep(String taskId){
        //删除设备关联关系
        List<WorkStep> workSteps = workStepRepository.findByTaskTaskIdAndDeleted(taskId, false);
        if(!CollectionUtils.isEmpty(workSteps)){
            workSteps.forEach(workStep->workStep.setDeleted(true));
            workStepRepository.saveAll(workSteps);
        }
    }

    @Override
    public void changeStatus(String taskCode, SysDictItem statusDict) {
        SearchCondition searchCondition = new SearchCondition();
        searchCondition.setOpenProps(Lists.newArrayList("statusCode"));
        searchCondition.setSearchItems(SearchItems.builder()
                .item(new SearchItem("taskCode", taskCode, null, SearchItem.Operator.EQ))
                .item(new SearchItem("deleted",false, null, SearchItem.Operator.EQ))
                .build());
        List<ProductionTaskDTO> tasks = this.findAllWithCondition(searchCondition);
        if (CollectionUtils.isEmpty(tasks)) throw TASK_NOT_EXIST.buildException(taskCode);
        ProductionTaskDTO productionTaskDTO = tasks.stream().findFirst().get();
        String statusCode = productionTaskDTO.getStatusCode();
        TaskStatus taskStatus = TaskStatus.valueOf(statusCode);
        TaskStatus changeStatus = TaskStatus.valueOf(statusDict.getItemCode());
        if(taskStatus.getCode().equals(changeStatus.getCode())) {
            throw STATUS_CHANGE_ERROR.buildException(taskStatus.getMsg(),changeStatus.getMsg());
        }
        //todo 后续添加状态变更事件
        switch (taskStatus){
            case TaskStatus.not_start:
                if (!changeStatus.equals(TaskStatus.in_progress)) {
                    throw STATUS_CHANGE_ERROR.buildException(taskStatus.getMsg(),changeStatus.getMsg());
                }
                break;
            case TaskStatus.in_progress:
                if (!changeStatus.equals(TaskStatus.completed)) {
                    throw STATUS_CHANGE_ERROR.buildException(taskStatus.getMsg(),changeStatus.getMsg());
                }
                break;
            case TaskStatus.completed:
                throw STATUS_CHANGE_ERROR.buildException(taskStatus.getMsg(),changeStatus.getMsg());
            default:break;
        }
        repository.changeStatus(productionTaskDTO.getTaskId(),statusDict);

    }

    @Override
    public List<ProductionTask> findByTaskCodes(Collection<String> taskCodes) {
        SearchCondition searchCondition = new SearchCondition();
        searchCondition.setSearchItems(SearchItems.builder()
                .item(new SearchItem("taskCode", taskCodes, null, SearchItem.Operator.IN))
                .item(new SearchItem("deleted",false, null, SearchItem.Operator.EQ)).build());

        return findEntityWithCondition(searchCondition);
    }

    @Override
    public ProductionTask findOneByTaskCode(String taskCode) {
        SearchCondition searchCondition = new SearchCondition();
        searchCondition.setSearchItems(SearchItems.builder().item(new SearchItem("taskCode", taskCode, null, SearchItem.Operator.EQ)).build());
        return findOne(searchCondition);
    }

    @Override
    public ProductionTask findByTaskId(String taskId) {
        SearchCondition searchCondition = new SearchCondition();
        searchCondition.setSearchItems(SearchItems.builder().item(new SearchItem("taskId", taskId, null, SearchItem.Operator.EQ)).build());
        return findOne(searchCondition);
    }
}
