package com.bzlj.craft.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.bzlj.base.result.DataResult;
import com.bzlj.craft.dto.ProductionTaskDTO;
import com.bzlj.craft.dto.WorkStepDTO;
import com.bzlj.craft.entity.ParameterDefinition;
import com.bzlj.craft.entity.ProductionTask;
import com.bzlj.craft.entity.WorkStep;
import com.bzlj.craft.enums.ParamType;
import com.bzlj.craft.repository.ParameterDefinitionRepository;
import com.bzlj.craft.repository.WorkStepRepository;
import com.bzlj.craft.service.ISurveillanceService;
import com.bzlj.craft.service.ITaskDataComparisonService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description:
 * @date 2025-06-10 14:07
 */
@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class TaskDataComparisonServiceImpl implements ITaskDataComparisonService {

    @Autowired
    private ISurveillanceService surveillanceService;

    @Autowired
    private ParameterDefinitionRepository parameterDefinitionRepository;

    @Autowired
    private WorkStepRepository workStepRepository;

    @Override
    public DataResult paramComparison(List<String> taskIds,String baseTaskId) {
        //查询基准任务
        ProductionTask baseTask = surveillanceService.findByTaskId(baseTaskId);
        //根据基准任务查询执行工步
        List<WorkStepDTO> workSteps = surveillanceService.findWorkStepByTask(surveillanceService.convertToDto(baseTask, ProductionTaskDTO.class));
        List<String> otherTaskIds = taskIds.stream().filter(taskId -> !taskId.equals(baseTaskId)).collect(Collectors.toList());
        //获取参数定义
        if(!CollectionUtil.isEmpty(workSteps)){
            List<String> workStepNames = workSteps.stream().map(WorkStepDTO::getWorkStepName).collect(Collectors.toList());
            //根据执行工步名称及其他对比任务获取执行工步
            List<WorkStep> otherWorkSteps = workStepRepository.findByTaskTaskIdInAndWorkStepNameInAndDeleted(otherTaskIds, workStepNames, false);

            List<ParameterDefinition> parameterDefinitions = parameterDefinitionRepository.findByWorkStepWorkStepIdInAndParamType_ItemCodeIn(workStepIds, Lists.newArrayList(ParamType.NUMBER.getCode(), ParamType.TEXT.getCode(), ParamType.DATE.getCode()));

        }



        //查询所有执行工步绑定的参数定义

        //根据参数定义获取实际值

        //按照任务分类组装数据
        return null;
    }
}
