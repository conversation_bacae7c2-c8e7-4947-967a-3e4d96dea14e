package com.bzlj.message.internaldistribution.config;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.redisson.config.ReadMode;
import org.redisson.config.SubscriptionMode;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Redisson 配置类
 * 支持单机、集群、哨兵三种部署模式
 *
 * <AUTHOR>
 * @date 2025/6/20
 */
@Slf4j
@Configuration
@RequiredArgsConstructor
@EnableConfigurationProperties(RedissonProperties.class)
public class RedisConfig {

    private final RedissonProperties redissonProperties;

    @Bean
    public RedissonClient redissonClient() {
        Config config = new Config();
        String mode = redissonProperties.getMode().toLowerCase();

        log.info("初始化 Redisson 客户端，部署模式: {}", mode);

        switch (mode) {
            case "single":
                configureSingleServer(config);
                break;
            case "cluster":
                configureClusterServer(config);
                break;
            case "sentinel":
                configureSentinelServer(config);
                break;
            default:
                log.warn("未知的 Redis 部署模式: {}，使用默认单机模式", mode);
                configureSingleServer(config);
                break;
        }

        return Redisson.create(config);
    }

    /**
     * 配置单机模式
     */
    private void configureSingleServer(Config config) {
        RedissonProperties.SingleServerConfig singleConfig = redissonProperties.getSingleServerConfig();

        log.info("配置 Redis 单机模式: {}", singleConfig.getAddress());

        config.useSingleServer()
                .setAddress(singleConfig.getAddress())
                .setDatabase(singleConfig.getDatabase())
                .setConnectTimeout(singleConfig.getConnectTimeout())
                .setTimeout(singleConfig.getTimeout())
                .setRetryAttempts(singleConfig.getRetryAttempts())
                .setRetryInterval(singleConfig.getRetryInterval())
                .setConnectionPoolSize(singleConfig.getConnectionPoolSize())
                .setConnectionMinimumIdleSize(singleConfig.getConnectionMinimumIdleSize())
                .setIdleConnectionTimeout(singleConfig.getIdleConnectionTimeout())
                .setPingConnectionInterval(singleConfig.getPingConnectionInterval())
                .setKeepAlive(singleConfig.isKeepAlive());

        // 设置密码
        if (singleConfig.getPassword() != null && !singleConfig.getPassword().trim().isEmpty()) {
            config.useSingleServer().setPassword(singleConfig.getPassword());
        }
    }

    /**
     * 配置集群模式
     */
    private void configureClusterServer(Config config) {
        RedissonProperties.ClusterConfig clusterConfig = redissonProperties.getClusterConfig();

        log.info("配置 Redis 集群模式，节点数量: {}", clusterConfig.getNodeAddresses().size());
        log.info("集群节点: {}", clusterConfig.getNodeAddresses());

        config.useClusterServers()
                .addNodeAddress(clusterConfig.getNodeAddresses().toArray(new String[0]))
                .setConnectTimeout(clusterConfig.getConnectTimeout())
                .setTimeout(clusterConfig.getTimeout())
                .setRetryAttempts(clusterConfig.getRetryAttempts())
                .setRetryInterval(clusterConfig.getRetryInterval())
                .setMasterConnectionPoolSize(clusterConfig.getMasterConnectionPoolSize())
                .setSlaveConnectionPoolSize(clusterConfig.getSlaveConnectionPoolSize())
                .setMasterConnectionMinimumIdleSize(clusterConfig.getMasterConnectionMinimumIdleSize())
                .setSlaveConnectionMinimumIdleSize(clusterConfig.getSlaveConnectionMinimumIdleSize())
                .setIdleConnectionTimeout(clusterConfig.getIdleConnectionTimeout())
                .setPingConnectionInterval(clusterConfig.getPingConnectionInterval())
                .setKeepAlive(clusterConfig.isKeepAlive())
                .setScanInterval(clusterConfig.getScanInterval())
                .setReadMode(parseReadMode(clusterConfig.getReadMode()))
                .setSubscriptionMode(parseSubscriptionMode(clusterConfig.getSubscriptionMode()));

        // 设置密码
        if (clusterConfig.getPassword() != null && !clusterConfig.getPassword().trim().isEmpty()) {
            config.useClusterServers().setPassword(clusterConfig.getPassword());
        }
    }

    /**
     * 配置哨兵模式
     */
    private void configureSentinelServer(Config config) {
        RedissonProperties.SentinelConfig sentinelConfig = redissonProperties.getSentinelConfig();

        log.info("配置 Redis 哨兵模式，主服务器: {}", sentinelConfig.getMasterName());
        log.info("哨兵节点数量: {}", sentinelConfig.getSentinelAddresses().size());
        log.info("哨兵节点: {}", sentinelConfig.getSentinelAddresses());

        config.useSentinelServers()
                .setMasterName(sentinelConfig.getMasterName())
                .addSentinelAddress(sentinelConfig.getSentinelAddresses().toArray(new String[0]))
                .setDatabase(sentinelConfig.getDatabase())
                .setConnectTimeout(sentinelConfig.getConnectTimeout())
                .setTimeout(sentinelConfig.getTimeout())
                .setRetryAttempts(sentinelConfig.getRetryAttempts())
                .setRetryInterval(sentinelConfig.getRetryInterval())
                .setMasterConnectionPoolSize(sentinelConfig.getMasterConnectionPoolSize())
                .setSlaveConnectionPoolSize(sentinelConfig.getSlaveConnectionPoolSize())
                .setMasterConnectionMinimumIdleSize(sentinelConfig.getMasterConnectionMinimumIdleSize())
                .setSlaveConnectionMinimumIdleSize(sentinelConfig.getSlaveConnectionMinimumIdleSize())
                .setIdleConnectionTimeout(sentinelConfig.getIdleConnectionTimeout())
                .setPingConnectionInterval(sentinelConfig.getPingConnectionInterval())
                .setKeepAlive(sentinelConfig.isKeepAlive())
                .setReadMode(parseReadMode(sentinelConfig.getReadMode()))
                .setSubscriptionMode(parseSubscriptionMode(sentinelConfig.getSubscriptionMode()));

        // 设置 Redis 密码
        if (sentinelConfig.getPassword() != null && !sentinelConfig.getPassword().trim().isEmpty()) {
            config.useSentinelServers().setPassword(sentinelConfig.getPassword());
        }

        // 设置哨兵密码
        if (sentinelConfig.getSentinelPassword() != null && !sentinelConfig.getSentinelPassword().trim().isEmpty()) {
            config.useSentinelServers().setSentinelPassword(sentinelConfig.getSentinelPassword());
        }
    }

    /**
     * 解析读取模式
     */
    private ReadMode parseReadMode(String readMode) {
        try {
            return ReadMode.valueOf(readMode.toUpperCase());
        } catch (Exception e) {
            log.warn("无效的读取模式: {}，使用默认值 SLAVE", readMode);
            return ReadMode.SLAVE;
        }
    }

    /**
     * 解析订阅模式
     */
    private SubscriptionMode parseSubscriptionMode(String subscriptionMode) {
        try {
            return SubscriptionMode.valueOf(subscriptionMode.toUpperCase());
        } catch (Exception e) {
            log.warn("无效的订阅模式: {}，使用默认值 MASTER", subscriptionMode);
            return SubscriptionMode.MASTER;
        }
    }
}
