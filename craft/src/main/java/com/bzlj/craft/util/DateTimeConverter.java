package com.bzlj.craft.util;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;

public class DateTimeConverter {
    public static LocalDateTime convertToLocalDateTime(String dateTimeStr) {
        // 定义日期时间格式：yyyyMMddHHmmss（14位数字）
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
        
        try {
            return LocalDateTime.parse(dateTimeStr, formatter);
        } catch (DateTimeParseException e) {
            throw new IllegalArgumentException("日期时间格式无效或值不合法: " + dateTimeStr, e);
        }
    }
}