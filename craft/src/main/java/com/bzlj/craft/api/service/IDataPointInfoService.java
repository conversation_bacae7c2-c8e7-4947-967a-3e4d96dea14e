package com.bzlj.craft.api.service;

import com.bzlj.base.service.IBaseService;
import com.bzlj.craft.dto.DataPointInfoDTO;
import com.bzlj.craft.entity.DataPointInfo;

import java.util.List;

/**
 * <AUTHOR>
 * @description:
 * @date 2025-03-13 11:34
 */
public interface IDataPointInfoService extends IBaseService<DataPointInfo, DataPointInfoDTO, String> {

    List<DataPointInfo> findByProcessStepIds(List<String> processStepIds);
}
