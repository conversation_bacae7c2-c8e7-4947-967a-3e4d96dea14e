<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE suite SYSTEM "http://testng.org/testng-1.0.dtd">
<suite name="MessageExchangeTestSuite" verbose="1" parallel="methods" thread-count="3">
    <test name="MessageStatisticsTests">
        <classes>
            <class name="com.bzlj.message.internaldistribution.service.MessageStatisticsServiceTest"/>
            <class name="com.bzlj.message.internaldistribution.util.RedisUtilsTest"/>
            <class name="com.bzlj.message.internaldistribution.controller.MessageStatisticsControllerTest"/>
        </classes>
    </test>

    <test name="MessageServiceTests">
        <classes>
            <class name="com.bzlj.message.service.MsgConsumeServiceTest"/>
        </classes>
    </test>
</suite>
