package com.bzlj.craft.service.impl;

import com.bzlj.base.search.SearchCondition;
import com.bzlj.base.search.SearchItem;
import com.bzlj.base.search.SearchItems;
import com.bzlj.craft.api.service.IProcessParameterService;
import com.bzlj.craft.dto.*;
import com.bzlj.craft.entity.*;
import com.bzlj.craft.repository.ProcessStepRepository;
import com.bzlj.craft.repository.StepParameterRepository;
import com.bzlj.craft.repository.WorkStepRepository;
import com.bzlj.craft.service.ISurveillanceService;
import com.bzlj.craft.service.ITaskQueryService;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

/**
 * 任务查询服务实现类
 * 
 * <AUTHOR>
 * @date 2025-06-18
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TaskQueryServiceImpl implements ITaskQueryService {
    
    private final ISurveillanceService surveillanceService;
    private final WorkStepRepository workStepRepository;
    private final ProcessStepRepository processStepRepository;
    private final StepParameterRepository stepParameterRepository;
    private final IProcessParameterService processParameterService;
    
    @Qualifier("virtualThreadExecutor")
    private final Executor virtualThreadExecutor;
    
    @Override
    public CompletableFuture<TaskQueryResultDTO> queryTaskDetailsAsync(String taskId) {
        return CompletableFuture.supplyAsync(() -> queryTaskDetails(taskId), virtualThreadExecutor);
    }
    
    @Override
    public CompletableFuture<List<TaskQueryResultDTO>> queryMultipleTaskDetailsAsync(List<String> taskIds) {
        return CompletableFuture.supplyAsync(() -> {
            // 使用虚拟线程并行查询多个任务
            List<CompletableFuture<TaskQueryResultDTO>> futures = taskIds.stream()
                    .map(this::queryTaskDetailsAsync)
                    .collect(Collectors.toList());
            
            // 等待所有查询完成
            return futures.stream()
                    .map(CompletableFuture::join)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
        }, virtualThreadExecutor);
    }
    
    @Override
    public TaskQueryResultDTO queryTaskDetails(String taskId) {
        try {
            log.info("开始查询任务详情，taskId: {}", taskId);
            long startTime = System.currentTimeMillis();
            
            // 查询任务基本信息
            ProductionTask task = surveillanceService.findByTaskId(taskId);
            if (task == null) {
                log.warn("任务不存在，taskId: {}", taskId);
                return null;
            }
            
            TaskQueryResultDTO result = new TaskQueryResultDTO();
            result.setTaskId(task.getTaskId());
            result.setTaskCode(task.getTaskCode());
            result.setTaskName(task.getTaskName());
            
            // 使用虚拟线程并行查询工步和参数
            CompletableFuture<List<WorkStepDetailDTO>> workStepsFuture = 
                    CompletableFuture.supplyAsync(() -> queryWorkSteps(task), virtualThreadExecutor);
            
            CompletableFuture<List<ProcessParameterDetailDTO>> parametersFuture = 
                    CompletableFuture.supplyAsync(() -> queryProcessParameters(task), virtualThreadExecutor);
            
            // 等待并行查询完成
            CompletableFuture<Void> allQueries = CompletableFuture.allOf(workStepsFuture, parametersFuture);
            allQueries.join();
            
            result.setWorkSteps(workStepsFuture.join());
            result.setProcessParameters(parametersFuture.join());
            
            long endTime = System.currentTimeMillis();
            log.info("任务详情查询完成，taskId: {}, 耗时: {}ms", taskId, endTime - startTime);
            
            return result;
        } catch (Exception e) {
            log.error("查询任务详情失败，taskId: {}", taskId, e);
            return null;
        }
    }
    
    /**
     * 查询任务的执行工步
     */
    private List<WorkStepDetailDTO> queryWorkSteps(ProductionTask task) {
        try {
            log.debug("开始查询任务工步，taskId: {}", task.getTaskId());
            
            List<WorkStepDetailDTO> workStepDetails = new ArrayList<>();
            
            if (task.getProcess() != null) {
                // 查询工序下的工步定义
                List<ProcessStep> processSteps = processStepRepository
                        .findByProcessIdOrderByStepOrderAsc(task.getProcess().getId());
                
                if (!CollectionUtils.isEmpty(processSteps)) {
                    List<String> stepIds = processSteps.stream()
                            .map(ProcessStep::getId)
                            .collect(Collectors.toList());
                    
                    // 查询任务下的执行工步
                    List<WorkStep> workSteps = workStepRepository
                            .findByStepIdInAndTaskTaskIdAndDeleted(stepIds, task.getTaskId(), false);
                    
                    // 使用虚拟线程并行处理每个工步的参数查询
                    List<CompletableFuture<WorkStepDetailDTO>> stepFutures = workSteps.stream()
                            .map(workStep -> CompletableFuture.supplyAsync(() -> 
                                    convertToWorkStepDetail(workStep), virtualThreadExecutor))
                            .collect(Collectors.toList());
                    
                    workStepDetails = stepFutures.stream()
                            .map(CompletableFuture::join)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());
                }
            }
            
            log.debug("任务工步查询完成，taskId: {}, 工步数量: {}", task.getTaskId(), workStepDetails.size());
            return workStepDetails;
        } catch (Exception e) {
            log.error("查询任务工步失败，taskId: {}", task.getTaskId(), e);
            return new ArrayList<>();
        }
    }
    
    /**
     * 查询任务的工艺参数
     */
    private List<ProcessParameterDetailDTO> queryProcessParameters(ProductionTask task) {
        try {
            log.debug("开始查询任务工艺参数，taskId: {}", task.getTaskId());
            
            // 查询任务下的所有工艺参数
            SearchCondition condition = new SearchCondition();
            condition.setSearchItems(SearchItems.builder()
                    .item(new SearchItem("task.taskId", task.getTaskId(), null, SearchItem.Operator.EQ))
                    .build());
            condition.setOpenProps(Lists.newArrayList("processTextParameter", "processDateParameter", 
                    "processNumParameter", "paramDef"));
            
            List<ProcessParameter> parameters = processParameterService.findEntityWithCondition(condition);
            
            List<ProcessParameterDetailDTO> parameterDetails = parameters.stream()
                    .map(this::convertToParameterDetail)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            
            log.debug("任务工艺参数查询完成，taskId: {}, 参数数量: {}", task.getTaskId(), parameterDetails.size());
            return parameterDetails;
        } catch (Exception e) {
            log.error("查询任务工艺参数失败，taskId: {}", task.getTaskId(), e);
            return new ArrayList<>();
        }
    }
    
    /**
     * 转换工步为详情DTO
     */
    private WorkStepDetailDTO convertToWorkStepDetail(WorkStep workStep) {
        try {
            WorkStepDetailDTO dto = new WorkStepDetailDTO();
            dto.setWorkStepId(workStep.getWorkStepId());
            dto.setWorkStepName(workStep.getWorkStepName());
            dto.setWorkStepOrder(workStep.getWorkStepOrder());
            dto.setStartTime(workStep.getStartTime());
            dto.setEndTime(workStep.getEndTime());
            
            // 查询工步下的参数定义
            if (workStep.getStep() != null) {
                List<StepParameter> stepParameters = stepParameterRepository
                        .findByStepIdOrderByCreatedTime(workStep.getStep().getId());
                
                List<ProcessParameterDetailDTO> stepParamDetails = stepParameters.stream()
                        .map(this::convertStepParameterToDetail)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
                
                dto.setStepParameters(stepParamDetails);
            }
            
            return dto;
        } catch (Exception e) {
            log.error("转换工步详情失败，workStepId: {}", workStep.getWorkStepId(), e);
            return null;
        }
    }
    
    /**
     * 转换工艺参数为详情DTO
     */
    private ProcessParameterDetailDTO convertToParameterDetail(ProcessParameter parameter) {
        try {
            ProcessParameterDetailDTO dto = new ProcessParameterDetailDTO();
            dto.setParameterId(parameter.getParameterId());
            
            if (parameter.getParamDef() != null) {
                dto.setParamDefId(parameter.getParamDef().getParamDefId());
                dto.setParameterName(parameter.getParamDef().getParameterName());
                dto.setParameterType(parameter.getParamDef().getParameterType());
                dto.setUnit(parameter.getParamDef().getUnit());
                dto.setTargetValue(parameter.getParamDef().getTargetValue());
                dto.setUpperLimit(parameter.getParamDef().getUpperLimit());
                dto.setLowerLimit(parameter.getParamDef().getLowerLimit());
            }
            
            // 根据参数类型设置实际值
            if (parameter.getProcessNumParameter() != null) {
                dto.setNumericValue(parameter.getProcessNumParameter().getActualValue());
                dto.setActualValue(parameter.getProcessNumParameter().getActualValue().toString());
                
                // 判断是否合格
                if (dto.getUpperLimit() != null && dto.getLowerLimit() != null) {
                    BigDecimal actualValue = parameter.getProcessNumParameter().getActualValue();
                    dto.setIsQualified(actualValue.compareTo(dto.getLowerLimit()) >= 0 && 
                                     actualValue.compareTo(dto.getUpperLimit()) <= 0);
                }
            } else if (parameter.getProcessTextParameter() != null) {
                dto.setTextValue(parameter.getProcessTextParameter().getActualValue());
                dto.setActualValue(parameter.getProcessTextParameter().getActualValue());
            } else if (parameter.getProcessDateParameter() != null) {
                dto.setDateValue(parameter.getProcessDateParameter().getActualValue());
                dto.setActualValue(parameter.getProcessDateParameter().getActualValue().toString());
            }
            
            dto.setCreatedTime(parameter.getCreatedTime());
            dto.setUpdatedTime(parameter.getUpdatedTime());
            
            return dto;
        } catch (Exception e) {
            log.error("转换工艺参数详情失败，parameterId: {}", parameter.getParameterId(), e);
            return null;
        }
    }
    
    /**
     * 转换工步参数定义为详情DTO
     */
    private ProcessParameterDetailDTO convertStepParameterToDetail(StepParameter stepParameter) {
        try {
            ProcessParameterDetailDTO dto = new ProcessParameterDetailDTO();
            dto.setParamDefId(stepParameter.getParamDefId());
            dto.setParameterName(stepParameter.getParameterName());
            dto.setParameterType(stepParameter.getParameterType());
            dto.setUnit(stepParameter.getUnit());
            dto.setTargetValue(stepParameter.getTargetValue());
            dto.setUpperLimit(stepParameter.getUpperLimit());
            dto.setLowerLimit(stepParameter.getLowerLimit());
            dto.setCreatedTime(stepParameter.getCreatedTime());
            
            return dto;
        } catch (Exception e) {
            log.error("转换工步参数定义失败，paramDefId: {}", stepParameter.getParamDefId(), e);
            return null;
        }
    }
}
