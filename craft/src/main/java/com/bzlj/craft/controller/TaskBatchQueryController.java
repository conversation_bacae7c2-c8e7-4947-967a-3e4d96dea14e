package com.bzlj.craft.controller;

import com.bzlj.base.response.UnifyResponse;
import com.bzlj.craft.dto.ProcessParameterDetailDTO;
import com.bzlj.craft.dto.TaskQueryResultDTO;
import com.bzlj.craft.dto.WorkStepDetailDTO;
import com.bzlj.craft.service.ITaskBatchQueryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 任务批量查询控制器
 * 提供基于虚拟线程的高性能批量任务查询接口
 * 
 * <AUTHOR>
 * @date 2025-06-18
 */
@Slf4j
@RestController
@RequestMapping("/task-batch-query")
@RequiredArgsConstructor
public class TaskBatchQueryController {
    
    private final ITaskBatchQueryService taskBatchQueryService;
    
    /**
     * 根据任务ID集合批量查询任务详情（同步接口）
     * 使用虚拟线程并行查询，提高查询效率
     * 
     * @param taskIds 任务ID列表
     * @return 任务查询结果映射
     */
    @PostMapping("/details")
    public UnifyResponse<Map<String, TaskQueryResultDTO>> batchQueryTaskDetails(@RequestBody List<String> taskIds) {
        log.info("批量查询任务详情，任务数量: {}", taskIds.size());
        
        if (taskIds.isEmpty()) {
            return UnifyResponse.error("任务ID列表不能为空");
        }
        
        if (taskIds.size() > 200) {
            return UnifyResponse.error("单次查询任务数量不能超过200个");
        }
        
        try {
            Map<String, TaskQueryResultDTO> results = taskBatchQueryService.batchQueryTaskDetails(taskIds);
            return UnifyResponse.success(results);
        } catch (Exception e) {
            log.error("批量查询任务详情失败", e);
            return UnifyResponse.error("批量查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 异步批量查询任务详情
     * 
     * @param taskIds 任务ID列表
     * @return 异步任务查询结果映射
     */
    @PostMapping("/details/async")
    public CompletableFuture<UnifyResponse<Map<String, TaskQueryResultDTO>>> batchQueryTaskDetailsAsync(
            @RequestBody List<String> taskIds) {
        log.info("异步批量查询任务详情，任务数量: {}", taskIds.size());
        
        if (taskIds.isEmpty()) {
            return CompletableFuture.completedFuture(UnifyResponse.error("任务ID列表不能为空"));
        }
        
        if (taskIds.size() > 200) {
            return CompletableFuture.completedFuture(UnifyResponse.error("单次查询任务数量不能超过200个"));
        }
        
        return taskBatchQueryService.batchQueryTaskDetailsAsync(taskIds)
                .thenApply(UnifyResponse::success)
                .exceptionally(throwable -> {
                    log.error("异步批量查询任务详情失败", throwable);
                    return UnifyResponse.error("异步批量查询失败: " + throwable.getMessage());
                });
    }
    
    /**
     * 批量查询任务的执行工步信息
     * 
     * @param taskIds 任务ID列表
     * @return 任务工步映射
     */
    @PostMapping("/work-steps")
    public UnifyResponse<Map<String, List<WorkStepDetailDTO>>> batchQueryWorkSteps(@RequestBody List<String> taskIds) {
        log.info("批量查询任务工步，任务数量: {}", taskIds.size());
        
        if (taskIds.isEmpty()) {
            return UnifyResponse.error("任务ID列表不能为空");
        }
        
        if (taskIds.size() > 200) {
            return UnifyResponse.error("单次查询任务数量不能超过200个");
        }
        
        try {
            Map<String, List<WorkStepDetailDTO>> results = taskBatchQueryService.batchQueryWorkSteps(taskIds);
            return UnifyResponse.success(results);
        } catch (Exception e) {
            log.error("批量查询任务工步失败", e);
            return UnifyResponse.error("批量查询工步失败: " + e.getMessage());
        }
    }
    
    /**
     * 批量查询任务的工艺参数信息
     * 
     * @param taskIds 任务ID列表
     * @return 任务参数映射
     */
    @PostMapping("/process-parameters")
    public UnifyResponse<Map<String, List<ProcessParameterDetailDTO>>> batchQueryProcessParameters(
            @RequestBody List<String> taskIds) {
        log.info("批量查询任务工艺参数，任务数量: {}", taskIds.size());
        
        if (taskIds.isEmpty()) {
            return UnifyResponse.error("任务ID列表不能为空");
        }
        
        if (taskIds.size() > 200) {
            return UnifyResponse.error("单次查询任务数量不能超过200个");
        }
        
        try {
            Map<String, List<ProcessParameterDetailDTO>> results = 
                    taskBatchQueryService.batchQueryProcessParameters(taskIds);
            return UnifyResponse.success(results);
        } catch (Exception e) {
            log.error("批量查询任务工艺参数失败", e);
            return UnifyResponse.error("批量查询工艺参数失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取查询性能统计信息
     * 对比串行查询和并行查询的性能差异
     * 
     * @param taskIds 任务ID列表
     * @return 性能统计信息
     */
    @PostMapping("/performance-stats")
    public UnifyResponse<Map<String, Object>> getQueryPerformanceStats(@RequestBody List<String> taskIds) {
        log.info("获取查询性能统计，任务数量: {}", taskIds.size());
        
        if (taskIds.isEmpty()) {
            return UnifyResponse.error("任务ID列表不能为空");
        }
        
        if (taskIds.size() > 50) {
            return UnifyResponse.error("性能测试任务数量不能超过50个");
        }
        
        try {
            Map<String, Object> stats = taskBatchQueryService.getQueryPerformanceStats(taskIds);
            return UnifyResponse.success(stats);
        } catch (Exception e) {
            log.error("获取查询性能统计失败", e);
            return UnifyResponse.error("获取性能统计失败: " + e.getMessage());
        }
    }
    
    /**
     * 健康检查接口
     * 
     * @return 服务状态
     */
    @GetMapping("/health")
    public UnifyResponse<Map<String, Object>> healthCheck() {
        Map<String, Object> health = Map.of(
                "status", "UP",
                "service", "TaskBatchQueryService",
                "timestamp", System.currentTimeMillis(),
                "virtualThreadSupport", true
        );
        return UnifyResponse.success(health);
    }
}
